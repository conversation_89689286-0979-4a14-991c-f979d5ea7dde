// Content script for Amazon Product Info Enhancer

// Main function to initialize the extension
function initAmazonEnhancer() {
  // Check if we're on an Amazon product listing page
  if (isProductListingPage()) {
    console.log('Amazon Product Info Enhancer: Product listing page detected');
    addFloatingIconsToProducts();

    // Add event listener for dynamic content loading (Amazon loads products dynamically)
    observePageChanges();
  }
}

// Check if the current page is a product listing page
function isProductListingPage() {
  // Amazon search results page
  if (window.location.href.includes('/s?') ||
      window.location.href.includes('/s/') ||
      window.location.href.includes('/gp/bestsellers') ||
      window.location.href.includes('/gp/new-releases')) {
    return true;
  }

  // Check for product grid elements that typically appear on listing pages
  const productGrids = document.querySelectorAll('.s-result-list, .s-search-results');
  return productGrids.length > 0;
}

// Add floating icons to all product images on the page
function addFloatingIconsToProducts() {
  // Find all product containers
  const productElements = document.querySelectorAll('.s-result-item');

  productElements.forEach((product, index) => {
    // Find the product image container
    const imageContainer = product.querySelector('.s-image-container, .a-section.aok-relative');
    if (!imageContainer) return;

    // Check if we already added an icon to this product
    if (imageContainer.querySelector('.amazon-enhancer-icon')) return;

    // Create floating icon
    const floatingIcon = document.createElement('div');
    floatingIcon.className = 'amazon-enhancer-icon';
    floatingIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>';

    // Add data attribute to identify the product
    floatingIcon.dataset.productIndex = index;

    // Create a wrapper for the icon to isolate it from parent links
    const iconWrapper = document.createElement('div');
    iconWrapper.className = 'amazon-enhancer-icon-wrapper';
    iconWrapper.style.position = 'absolute';
    iconWrapper.style.top = '10px';
    iconWrapper.style.right = '10px';
    iconWrapper.style.zIndex = '9999';
    iconWrapper.style.pointerEvents = 'auto';

    // Add extremely aggressive event prevention to the wrapper
    const preventAllEvents = (e) => {
      e.stopPropagation();
      e.preventDefault();
      e.stopImmediatePropagation();
      return false;
    };

    // Add click event listener with stronger event prevention
    floatingIcon.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      if (e.stopImmediatePropagation) {
        e.stopImmediatePropagation();
      }

      // Show our popup
      showProductInfoPopup(product, e);

      // Return false to prevent default behavior and event bubbling
      return false;
    }, true);

    // Also prevent all other events on the icon itself
    ['mousedown', 'mouseup', 'touchstart', 'touchend'].forEach(eventType => {
      floatingIcon.addEventListener(eventType, preventAllEvents, true);
    });

    // Add the icon to the wrapper
    iconWrapper.appendChild(floatingIcon);

    // Append wrapper to image container
    imageContainer.appendChild(iconWrapper);
  });
}

// Show product info popup when icon is clicked
function showProductInfoPopup(productElement, clickEvent) {
  // Extract product information
  const productInfo = extractProductInfo(productElement);

  // Create popup element if it doesn't exist
  let popup = document.getElementById('amazon-enhancer-popup');
  if (!popup) {
    popup = document.createElement('div');
    popup.id = 'amazon-enhancer-popup';
    document.body.appendChild(popup);
  }

  // Position popup near the clicked icon
  positionPopup(popup, clickEvent);

  // Fill popup with product information
  populatePopup(popup, productInfo);

  // Add close button and outside click handler
  setupPopupInteractions(popup);
}

// Extract product information from the product element
function extractProductInfo(productElement) {
  // Initialize product info object
  const info = {
    title: '',
    images: [],
    currentPrice: '',
    oldPrice: '',
    discount: '',
    rating: '',
    reviewCount: '',
    isPrime: false,
    aboutItem: '',
    description: '',
    shipping: '',
    specifications: '',
    productDetails: '',
    productOverview: '',
    reviews: []
  };

  // Extract title
  const titleElement = productElement.querySelector('h2');
  if (titleElement) {
    info.title = titleElement.textContent.trim();
  }

  // Extract current price
  const priceElement = productElement.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    info.currentPrice = priceElement.textContent.trim();
  }

  // Extract old price if available
  const oldPriceElement = productElement.querySelector('.a-price.a-text-price .a-offscreen');
  if (oldPriceElement) {
    info.oldPrice = oldPriceElement.textContent.trim();

    // Calculate discount percentage if both prices are available
    if (info.currentPrice && info.oldPrice) {
      try {
        const currentPriceValue = parseFloat(info.currentPrice.replace(/[^0-9.]/g, ''));
        const oldPriceValue = parseFloat(info.oldPrice.replace(/[^0-9.]/g, ''));
        if (!isNaN(currentPriceValue) && !isNaN(oldPriceValue) && oldPriceValue > 0) {
          const discountPercent = Math.round(((oldPriceValue - currentPriceValue) / oldPriceValue) * 100);
          info.discount = `${discountPercent}% off`;
        }
      } catch (e) {
        console.error('Error calculating discount:', e);
      }
    }
  }

  // Extract rating
  const ratingElement = productElement.querySelector('.a-icon-star-small');
  if (ratingElement) {
    info.rating = ratingElement.textContent.trim();
  }

  // Extract review count
  const reviewCountElement = productElement.querySelector('.a-size-base.s-underline-text');
  if (reviewCountElement) {
    info.reviewCount = reviewCountElement.textContent.trim();
  }

  // Check for Prime
  info.isPrime = !!productElement.querySelector('.s-prime');

  // We're not extracting seller information

  // Extract main image and try to find additional images
  const imageElement = productElement.querySelector('.s-image');
  if (imageElement && imageElement.src) {
    info.images.push(imageElement.src);

    // Try to get higher resolution image
    const highResImage = imageElement.src.replace(/_AC_UL\d+_/, '_AC_UL1500_');
    if (highResImage !== imageElement.src) {
      info.images.push(highResImage);
    }

    // Get product URL to fetch more details
    const productLink = productElement.querySelector('a.a-link-normal');
    if (productLink && productLink.href) {
      // Store the product URL for fetching additional details
      info.productUrl = productLink.href;

      // Fetch additional product details
      fetchProductDetails(info.productUrl, info);
    }
  }

  return info;
}

// Fetch additional product details from the product page
function fetchProductDetails(productUrl, productInfo) {
  // Use the background script to fetch the product page
  chrome.runtime.sendMessage(
    {
      action: 'fetchProductPage',
      url: productUrl
    },
    response => {
      if (response && response.status === 'success' && response.html) {
        try {
          // Create a DOM parser to parse the HTML
          const parser = new DOMParser();
          const doc = parser.parseFromString(response.html, 'text/html');

          // Extract additional images
          const additionalImages = doc.querySelectorAll('#altImages img, #imageBlock img, #imgTagWrapperId img');
          additionalImages.forEach(img => {
            if (img.src && !productInfo.images.includes(img.src)) {
              // Get high-res version if possible
              const highResImg = img.src.replace(/_AC_US\d+_/, '_AC_US1500_')
                                        .replace(/_SX\d+_/, '_SX1500_')
                                        .replace(/_SY\d+_/, '_SY1500_');
              productInfo.images.push(highResImg);
            }
          });

          // Try to extract image URLs from the image gallery data
          try {
            const scripts = doc.querySelectorAll('script');
            scripts.forEach(script => {
              if (script.textContent.includes('ImageBlockATF') || script.textContent.includes('imageGalleryData')) {
                const matches = script.textContent.match(/"(https:\/\/m\.media-amazon\.com\/images\/I\/[^"]+)"/g);
                if (matches) {
                  matches.forEach(match => {
                    const imgUrl = match.replace(/"/g, '');
                    if (!productInfo.images.includes(imgUrl)) {
                      productInfo.images.push(imgUrl);
                    }
                  });
                }
              }
            });
          } catch (e) {
            console.error('Error extracting image gallery data:', e);
          }

          // Extract "About this item" section
          const aboutItemSection = doc.querySelector('#feature-bullets');
          if (aboutItemSection) {
            const aboutHTML = aboutItemSection.innerHTML;
            productInfo.aboutItem = cleanText(aboutHTML);
          }

          // Helper function to clean text and decode HTML entities
          function cleanText(text) {
            // Create a textarea element to decode HTML entities
            const textarea = document.createElement('textarea');
            textarea.innerHTML = text;
            // Get decoded text and clean up extra whitespace
            let decodedText = textarea.value;
            // Replace multiple spaces, newlines, and tabs with a single space
            decodedText = decodedText.replace(/\s+/g, ' ');
            // Trim the text
            return decodedText.trim();
          }

          // Extract product overview
          const productOverviewSection = doc.querySelector('#productOverview_feature_div, #dpx-product-overview_feature_div');
          if (productOverviewSection) {
            // Get the HTML content to preserve formatting
            const overviewHTML = productOverviewSection.innerHTML;
            // Clean up the text
            productInfo.productOverview = cleanText(overviewHTML);
          }

          // Extract product specifications
          const techSpecsTable = doc.querySelector('#productDetails_techSpec_section_1, #techSpecsTable');
          if (techSpecsTable) {
            const specsHTML = techSpecsTable.innerHTML;
            productInfo.specifications = cleanText(specsHTML);
          }

          // Extract product details
          const detailBullets = doc.querySelector('#detailBullets_feature_div');
          const productDetailsTable = doc.querySelector('#productDetails_db_sections');

          if (detailBullets) {
            const detailsHTML = detailBullets.innerHTML;
            productInfo.productDetails = cleanText(detailsHTML);
          } else if (productDetailsTable) {
            const detailsHTML = productDetailsTable.innerHTML;
            productInfo.productDetails = cleanText(detailsHTML);
          }

          // Extract product description
          const descriptionSection = doc.querySelector('#productDescription');
          if (descriptionSection) {
            const descHTML = descriptionSection.innerHTML;
            productInfo.description = cleanText(descHTML);
          }

          // Extract shipping information
          const deliveryBlock = doc.querySelector('#deliveryBlockMessage, #mir-layout-DELIVERY_BLOCK');
          if (deliveryBlock) {
            const shippingHTML = deliveryBlock.innerHTML;
            productInfo.shipping = cleanText(shippingHTML);
          }

          // Extract reviews
          const reviewsSection = doc.querySelector('#cm-cr-dp-review-list, #customer-reviews_feature_div');
          if (reviewsSection) {
            // Find individual review elements
            const reviewElements = reviewsSection.querySelectorAll('.review, .a-section.review, .a-section.celwidget');

            // Process up to 6 reviews
            let reviewCount = 0;
            reviewElements.forEach(reviewElement => {
              if (reviewCount >= 6) return;

              // Extract review data
              const reviewData = {
                rating: '',
                title: '',
                author: '',
                date: '',
                verified: false,
                content: ''
              };

              // Extract rating
              const ratingElement = reviewElement.querySelector('.a-icon-star, .a-star-rating');
              if (ratingElement) {
                reviewData.rating = ratingElement.textContent.trim();
              }

              // Extract title
              const titleElement = reviewElement.querySelector('.review-title, .a-size-base.review-title');
              if (titleElement) {
                reviewData.title = cleanText(titleElement.innerHTML);
              }

              // Extract author
              const authorElement = reviewElement.querySelector('.a-profile-name');
              if (authorElement) {
                reviewData.author = authorElement.textContent.trim();
              }

              // Extract date
              const dateElement = reviewElement.querySelector('.review-date');
              if (dateElement) {
                reviewData.date = dateElement.textContent.trim();
              }

              // Check if verified purchase
              const verifiedElement = reviewElement.querySelector('.a-color-state.a-text-bold');
              if (verifiedElement && verifiedElement.textContent.includes('Verified Purchase')) {
                reviewData.verified = true;
              }

              // Extract content
              const contentElement = reviewElement.querySelector('.review-text, .review-text-content');
              if (contentElement) {
                reviewData.content = cleanText(contentElement.innerHTML);
              }

              // Add review to the list if it has content
              if (reviewData.content) {
                productInfo.reviews.push(reviewData);
                reviewCount++;
              }
            });
          }

          // Update any popup that's currently showing this product
          updateProductInfoInPopup(productInfo);

        } catch (e) {
          console.error('Error parsing product page:', e);
          chrome.runtime.sendMessage({
            action: 'log',
            message: 'Error parsing product page: ' + e.toString()
          });
        }
      } else {
        console.error('Error fetching product page:', response);
        chrome.runtime.sendMessage({
          action: 'log',
          message: 'Error fetching product page: ' + JSON.stringify(response)
        });
      }
    }
  );
}

// Update product info in an open popup if it exists
function updateProductInfoInPopup(productInfo) {
  const popup = document.getElementById('amazon-enhancer-popup');
  if (popup) {
    // Check if this popup is for the same product
    const popupTitle = popup.querySelector('h3');
    if (popupTitle && popupTitle.textContent === productInfo.title) {
      // Update the popup with the new information
      populatePopup(popup, productInfo);
    }
  }
}

// Position the popup near the clicked icon
function positionPopup(popup, clickEvent) {
  // Get the target element (either the icon or its wrapper)
  const target = clickEvent.target.classList.contains('amazon-enhancer-icon') ?
                clickEvent.target :
                clickEvent.target.querySelector('.amazon-enhancer-icon') || clickEvent.target;

  const rect = target.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  // Position the popup below the icon
  popup.style.top = (rect.bottom + scrollTop + 10) + 'px';
  popup.style.left = (rect.left + scrollLeft) + 'px';

  // Make sure popup is visible in viewport
  setTimeout(() => {
    const popupRect = popup.getBoundingClientRect();
    if (popupRect.right > window.innerWidth) {
      popup.style.left = (window.innerWidth - popupRect.width - 20) + 'px';
    }
    if (popupRect.bottom > window.innerHeight) {
      popup.style.top = (rect.top + scrollTop - popupRect.height - 10) + 'px';
    }
  }, 0);
}

// Fill the popup with product information
function populatePopup(popup, productInfo) {
  popup.innerHTML = `
    <div class="amazon-enhancer-popup-header">
      <h3>${productInfo.title}</h3>
      <button class="amazon-enhancer-close-btn">&times;</button>
    </div>
    <div class="amazon-enhancer-popup-content">
      <div class="amazon-enhancer-image-gallery">
        ${productInfo.images.map(img => `<img src="${img}" alt="${productInfo.title}">`).join('')}
      </div>

      <div class="amazon-enhancer-price-section">
        <span class="amazon-enhancer-current-price">${productInfo.currentPrice}</span>
        ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
        ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
      </div>

      <div class="amazon-enhancer-rating-section">
        ${productInfo.rating ? `<span class="amazon-enhancer-rating">${productInfo.rating}</span>` : ''}
        ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : ''}
      </div>

      ${productInfo.isPrime ? '<div class="amazon-enhancer-prime-badge">Prime</div>' : ''}

      ${productInfo.shipping ? `
        <div class="amazon-enhancer-section">
          <h4>Shipping Information</h4>
          <div class="amazon-enhancer-shipping">${productInfo.shipping}</div>
        </div>
      ` : ''}

      ${productInfo.aboutItem ? `
        <div class="amazon-enhancer-section">
          <h4>About This Item</h4>
          <div class="amazon-enhancer-about-item">${productInfo.aboutItem}</div>
        </div>
      ` : ''}

      ${productInfo.productOverview ? `
        <div class="amazon-enhancer-section">
          <h4>Product Overview</h4>
          <div class="amazon-enhancer-product-overview">${productInfo.productOverview}</div>
        </div>
      ` : ''}

      ${productInfo.description ? `
        <div class="amazon-enhancer-section">
          <h4>Product Description</h4>
          <div class="amazon-enhancer-description">${productInfo.description}</div>
        </div>
      ` : ''}

      ${productInfo.specifications ? `
        <div class="amazon-enhancer-section">
          <h4>Product Specifications</h4>
          <div class="amazon-enhancer-specifications">${productInfo.specifications}</div>
        </div>
      ` : ''}

      ${productInfo.productDetails ? `
        <div class="amazon-enhancer-section">
          <h4>Product Details</h4>
          <div class="amazon-enhancer-product-details">${productInfo.productDetails}</div>
        </div>
      ` : ''}

      ${productInfo.reviews && productInfo.reviews.length > 0 ? `
        <div class="amazon-enhancer-section">
          <h4>Top Reviews</h4>
          <div class="amazon-enhancer-reviews">
            ${productInfo.reviews.map(review => `
              <div class="amazon-enhancer-review">
                <div class="amazon-enhancer-review-header">
                  <span class="amazon-enhancer-review-rating">${review.rating}</span>
                  <span class="amazon-enhancer-review-title">${review.title}</span>
                </div>
                <div class="amazon-enhancer-review-meta">
                  <span class="amazon-enhancer-review-author">By ${review.author}</span>
                  <span class="amazon-enhancer-review-date">on ${review.date}</span>
                  ${review.verified ? '<span class="amazon-enhancer-review-verified">Verified Purchase</span>' : ''}
                </div>
                <div class="amazon-enhancer-review-content">${review.content}</div>
              </div>
            `).join('')}
          </div>
        </div>
      ` : ''}

      ${productInfo.productUrl ? `
        <div class="amazon-enhancer-section">
          <a href="${productInfo.productUrl}" target="_blank" class="amazon-enhancer-view-button">
            View Full Product Page
          </a>
        </div>
      ` : ''}
    </div>
  `;
}

// Set up popup interactions (close button, outside click)
function setupPopupInteractions(popup) {
  // Close button click
  const closeBtn = popup.querySelector('.amazon-enhancer-close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      popup.remove();
      return false;
    });
  }

  // Click outside popup
  document.addEventListener('click', function closePopupOnOutsideClick(e) {
    if (!popup.contains(e.target) &&
        !e.target.classList.contains('amazon-enhancer-icon') &&
        !e.target.classList.contains('amazon-enhancer-icon-wrapper')) {
      popup.remove();
      document.removeEventListener('click', closePopupOnOutsideClick);
    }
  });

  // Prevent clicks inside popup from closing it or triggering Amazon's links
  popup.addEventListener('click', (e) => {
    // Only stop propagation for clicks that aren't on links to the product page
    const isProductLink = e.target.tagName === 'A' &&
                         e.target.classList.contains('amazon-enhancer-view-button');

    if (!isProductLink) {
      e.stopPropagation();
      e.preventDefault();
    }
  });

  // Handle links inside the popup
  const links = popup.querySelectorAll('a:not(.amazon-enhancer-view-button)');
  links.forEach(link => {
    link.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      return false;
    });
  });
}

// Observe page changes for dynamically loaded content
function observePageChanges() {
  const observer = new MutationObserver((mutations) => {
    let shouldRefresh = false;

    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any added nodes are product elements or contain product elements
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.classList && (
                node.classList.contains('s-result-item') ||
                node.querySelector('.s-result-item')
            )) {
              shouldRefresh = true;
            }
          }
        });
      }
    });

    if (shouldRefresh) {
      addFloatingIconsToProducts();
    }
  });

  // Start observing the document with the configured parameters
  observer.observe(document.body, { childList: true, subtree: true });
}

// Initialize the extension when the page is fully loaded
window.addEventListener('load', initAmazonEnhancer);

// Also run on DOM content loaded to handle static content
document.addEventListener('DOMContentLoaded', initAmazonEnhancer);
