// Content script for Amazon Product Info Enhancer
'use strict';

// Prevent multiple initializations
if (typeof window.amazonEnhancerInitialized === 'undefined') {
  window.amazonEnhancerInitialized = true;

  // Constants - protected inside initialization guard
  const CONSTANTS = {
    EXTENSION_NAME: 'Amazon Product Info Enhancer',
    MAX_REVIEWS: 6,
    MAX_COMPARISON_REVIEWS: 5,
    MAX_IMAGES: 10,
    POPUP_Z_INDEX: 999999,
    ICON_Z_INDEX: 9999,
    HOVER_DELAY: 100,
    FETCH_TIMEOUT: 10000,
    AFFILIATE_TAG: 'comproduct-20',
    STORAGE_KEYS: {
      COMPARISON_LIST: 'amazonEnhancerComparisonList'
    },
    SELECTORS: {
      PRODUCT_CONTAINERS: [
        // Search results and listings
        '.s-result-item',
        '.s-widget-container',
        '.sg-col-inner',
        '[data-component-type="s-search-result"]',
        '[data-asin]',

        // Category and bestseller pages
        '.a-cardui',
        '.octopus-pc-card',
        '.p13n-sc-uncoverable-faceout',
        '.zg-item-immersion',
        '.zg_item',
        '.zg-item',

        // Deal pages
        '.dealContainer',
        '.deal-card',
        '.deal-tile',

        // Carousel and recommendations
        '.a-carousel-card',
        '.a-carousel-viewport .a-carousel-card',
        '.rush-component[data-asin]',

        // Product grids and tiles
        '.a-section.a-spacing-base',
        '.a-section.a-spacing-small',
        '.a-section[data-asin]',

        // Mobile and responsive layouts
        '.s-mobile-result-item',
        '.s-desktop-result-item',

        // International variations
        '.puis-card-container',
        '.AdHolder',
        '.s-sponsored-list-item',

        // Generic fallbacks
        '[data-uuid]',
        '[data-index]',
        '.a-link-normal[href*="/dp/"]',
        '.a-link-normal[href*="/gp/product/"]'
      ],
      IMAGE_CONTAINERS: [
        // Primary image containers
        '.s-image-container',
        '.a-image-container',
        '.a-section.aok-relative',

        // Specific page types
        '.octopus-pc-item-image-container',
        '.p13n-sc-uncoverable-faceout img',
        '.zg-item img',
        '.dealContainer img',
        '.a-carousel-card img',
        '.rush-component img',

        // Image elements with Amazon URLs
        'img[data-src*="images-amazon"]',
        'img[src*="images-amazon"]',
        'img[data-src*="ssl-images-amazon"]',
        'img[src*="ssl-images-amazon"]',
        'img[data-src*="m.media-amazon"]',
        'img[src*="m.media-amazon"]',

        // Generic image selectors
        'img[data-src]',
        'img[src]',
        '.a-link-normal img',
        '.s-image',
        '.a-dynamic-image',

        // Fallback containers
        '.a-section img',
        '.a-cardui img',
        'a[href*="/dp/"] img',
        'a[href*="/gp/product/"] img'
      ]
    }
  };

  // List of all Amazon domains for global support
  const AMAZON_DOMAINS = Object.freeze([
    'amazon.com', 'amazon.co.uk', 'amazon.ca', 'amazon.de', 'amazon.fr',
    'amazon.it', 'amazon.es', 'amazon.co.jp', 'amazon.in', 'amazon.com.au',
    'amazon.com.mx', 'amazon.com.br', 'amazon.nl', 'amazon.se', 'amazon.pl',
    'amazon.sg', 'amazon.ae', 'amazon.sa', 'amazon.eg', 'amazon.com.tr',
    'amazon.cn', 'amazon.com.be', 'amazon.be'
  ]);

  // Safe Chrome runtime message sender with error handling
  function safeSendMessage(message, callback) {
    try {
      // Check if chrome.runtime is available and not invalidated
      if (typeof chrome !== 'undefined' &&
          chrome.runtime &&
          chrome.runtime.sendMessage &&
          !chrome.runtime.lastError &&
          chrome.runtime.id) {
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            console.warn(`${CONSTANTS.EXTENSION_NAME}: Chrome runtime error:`, chrome.runtime.lastError.message);
            if (callback && typeof callback === 'function') {
              callback({ success: false, error: chrome.runtime.lastError.message });
            }
          } else if (callback && typeof callback === 'function') {
            callback(response);
          }
        });
      } else {
        // Runtime not available - this is normal for content scripts in some contexts
        console.debug(`${CONSTANTS.EXTENSION_NAME}: Chrome runtime not available, using fallback for:`, JSON.stringify(message));
        if (callback && typeof callback === 'function') {
          callback({ success: false, error: 'Chrome runtime not available' });
        }
      }
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error sending Chrome runtime message:`, error.message || error);
      if (callback && typeof callback === 'function') {
        callback({ success: false, error: error.message || 'Unknown error' });
      }
    }
  }

  // Utility functions
  const Utils = {
    // Function to check if current page is on an Amazon domain
    isAmazonDomain() {
      try {
        const hostname = window.location.hostname.toLowerCase();
        return AMAZON_DOMAINS.some(domain => hostname.includes(domain));
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error checking Amazon domain:`, error);
        return false;
      }
    },

    // Debounce function for performance optimization
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // Throttle function for performance optimization
    throttle(func, limit) {
      let inThrottle;
      return function(...args) {
        if (!inThrottle) {
          func.apply(this, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    // Safe element query selector with error handling
    safeQuerySelector(element, selector) {
      try {
        return element ? element.querySelector(selector) : null;
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error in querySelector:`, error);
        return null;
      }
    },

    // Safe element query selector all with error handling
    safeQuerySelectorAll(element, selector) {
      try {
        return element ? Array.from(element.querySelectorAll(selector)) : [];
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error in querySelectorAll:`, error);
        return [];
      }
    },

    // Clean and validate text content
    cleanText(text) {
      if (!text || typeof text !== 'string') return '';
      return text.trim().replace(/\s+/g, ' ').substring(0, 5000); // Limit length
    },

    // Validate URL
    isValidUrl(string) {
      try {
        new URL(string);
        return true;
      } catch (_) {
        return false;
      }
    },

    // Generate unique ID
    generateId() {
      return `amazon-enhancer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
  };

  // Global storage for product comparison
  let comparisonList = [];

  // Comparison list management with enhanced error handling
  const ComparisonManager = {
    // Load comparison list from localStorage
    load() {
      try {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Loading comparison list from localStorage`);
        const savedList = localStorage.getItem('amazon-enhancer-comparison-list');
        if (savedList) {
          const parsed = JSON.parse(savedList);
          if (Array.isArray(parsed)) {
            // Validate and clean items
            comparisonList = parsed.filter(item =>
              item &&
              item.productUrl &&
              Utils.isValidUrl(item.productUrl) &&
              item.title &&
              Utils.cleanText(item.title)
            );
            console.log(`${CONSTANTS.EXTENSION_NAME}: Loaded ${comparisonList.length} valid items from comparison list`);
            updateCompareButton();
          } else {
            throw new Error('Invalid comparison list format');
          }
        } else {
          console.log(`${CONSTANTS.EXTENSION_NAME}: No saved comparison list found`);
          comparisonList = [];
        }
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error loading comparison list:`, error);
        comparisonList = [];
        this.save(); // Save empty list to fix corruption
      }
    },

    // Save comparison list to localStorage
    save() {
      try {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Saving comparison list to localStorage:`, comparisonList.length, 'products');

        // Validate and clean the list before saving
        const validItems = comparisonList.filter(item =>
          item &&
          item.productUrl &&
          Utils.isValidUrl(item.productUrl) &&
          item.title &&
          Utils.cleanText(item.title)
        ).map(item => ({
          ...item,
          title: Utils.cleanText(item.title),
          addedAt: item.addedAt || Date.now()
        }));

        if (validItems.length !== comparisonList.length) {
          console.warn(`${CONSTANTS.EXTENSION_NAME}: Cleaned ${comparisonList.length - validItems.length} invalid items from comparison list`);
          comparisonList = validItems;
        }

        const jsonString = JSON.stringify(comparisonList);
        localStorage.setItem('amazon-enhancer-comparison-list', jsonString);
        console.log(`${CONSTANTS.EXTENSION_NAME}: Comparison list saved successfully`);
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error saving comparison list:`, error);
      }
    },

    // Add or remove product from comparison list
    toggle(productInfo) {
      try {
        if (!productInfo || !productInfo.productUrl || !productInfo.title) {
          console.error(`${CONSTANTS.EXTENSION_NAME}: Invalid product info for comparison:`, productInfo);
          return false;
        }

        console.log(`${CONSTANTS.EXTENSION_NAME}: Toggling product in comparison:`, Utils.cleanText(productInfo.title));

        // Check if product is already in the list
        const existingIndex = comparisonList.findIndex(p => p.productUrl === productInfo.productUrl);
        console.log(`${CONSTANTS.EXTENSION_NAME}: Existing index:`, existingIndex, 'Current list length:', comparisonList.length);

        if (existingIndex >= 0) {
          // Product already in list, remove it
          console.log(`${CONSTANTS.EXTENSION_NAME}: Removing product from comparison list`);
          comparisonList.splice(existingIndex, 1);
          this.save();
          updateCompareButton();
          return false; // Indicates product was removed
        } else {
          // Add product to list (limit to 4 products)
          if (comparisonList.length < 4) {
            console.log(`${CONSTANTS.EXTENSION_NAME}: Adding product to comparison list`);
            comparisonList.push({
              ...productInfo,
              title: Utils.cleanText(productInfo.title),
              addedAt: Date.now()
            });
            this.save();
            updateCompareButton();
            return true; // Indicates product was added
          } else {
            // Show user-friendly notification instead of alert
            this.showNotification('You can compare up to 4 products at a time. Please remove a product before adding a new one.');
            return false;
          }
        }
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error toggling product in comparison:`, error);
        return false;
      }
    },

    // Update existing product in comparison list
    update(productInfo) {
      try {
        if (!productInfo || !productInfo.productUrl) return false;

        const productIndex = comparisonList.findIndex(p => p.productUrl === productInfo.productUrl);
        if (productIndex !== -1) {
          // Preserve the original addedAt timestamp
          const originalAddedAt = comparisonList[productIndex].addedAt;
          comparisonList[productIndex] = {
            ...comparisonList[productIndex],
            ...productInfo,
            title: Utils.cleanText(productInfo.title || comparisonList[productIndex].title),
            addedAt: originalAddedAt || Date.now()
          };
          this.save();
          console.log(`${CONSTANTS.EXTENSION_NAME}: Updated product in comparison:`, Utils.cleanText(productInfo.title));
          return true;
        }
        return false;
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error updating comparison list:`, error);
        return false;
      }
    },

    // Show user notification
    showNotification(message) {
      try {
        // Create a temporary notification element
        const notification = document.createElement('div');
        notification.className = 'amazon-enhancer-notification';
        notification.textContent = message;
        notification.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: #ff9900;
          color: white;
          padding: 12px 20px;
          border-radius: 6px;
          z-index: ${CONSTANTS.POPUP_Z_INDEX + 1};
          font-family: Arial, sans-serif;
          font-size: 14px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          max-width: 300px;
          word-wrap: break-word;
        `;

        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 3000);
      } catch (error) {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Error showing notification:`, error);
        // Fallback to alert if notification fails
        alert(message);
      }
    }
  };

  // Legacy function for backward compatibility
  function addToCompare(productInfo) {
    return ComparisonManager.toggle(productInfo);
  }

  // Legacy function for backward compatibility
  function loadComparisonList() {
    return ComparisonManager.load();
  }

  // Legacy function for backward compatibility
  function saveComparisonList() {
    return ComparisonManager.save();
  }

  // Legacy function for backward compatibility
  function updateProductInComparisonList(productInfo) {
    return ComparisonManager.update(productInfo);
  }

  // Update the compare button state with enhanced error handling
  function updateCompareButton() {
    try {
      console.log(`${CONSTANTS.EXTENSION_NAME}: Updating compare button, list length:`, comparisonList.length);
      let compareButton = document.getElementById('amazon-enhancer-compare-button');

      if (comparisonList.length > 0) {
        // Create or update the compare button
        if (!compareButton) {
          console.log(`${CONSTANTS.EXTENSION_NAME}: Creating new compare button`);
          compareButton = document.createElement('div');
          compareButton.id = 'amazon-enhancer-compare-button';
          compareButton.innerHTML = `
            <span class="amazon-enhancer-compare-count">${comparisonList.length}</span>
            <span class="amazon-enhancer-compare-text">Compare Products</span>
          `;

          // Add click handler with error handling
          compareButton.addEventListener('click', function(e) {
            try {
              e.preventDefault();
              e.stopPropagation();
              console.log(`${CONSTANTS.EXTENSION_NAME}: Compare button clicked, showing popup`);
              showComparisonPopup();
              return false;
            } catch (error) {
              console.error(`${CONSTANTS.EXTENSION_NAME}: Error handling compare button click:`, error);
            }
          });

          document.body.appendChild(compareButton);
        } else {
          console.log(`${CONSTANTS.EXTENSION_NAME}: Updating existing compare button`);
          const countElement = Utils.safeQuerySelector(compareButton, '.amazon-enhancer-compare-count');
          if (countElement) {
            countElement.textContent = comparisonList.length;
          }
        }
        compareButton.style.display = 'flex';
      } else if (compareButton) {
        // Hide the compare button if list is empty
        console.log(`${CONSTANTS.EXTENSION_NAME}: Hiding compare button (empty list)`);
        compareButton.style.display = 'none';
      }
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error updating compare button:`, error);
    }
  }

  // Universal page detection - works with ALL Amazon pages that have products
  function isProductListingPage() {
    try {
      const currentUrl = window.location.href.toLowerCase();

      // Comprehensive URL pattern matching for all Amazon page types
      const urlPatterns = [
        // Search and browse
        '/s?', '/s/', '/search/', '/browse/',

        // Category pages
        '/b/', '/l/', '/n/', '/c/', '/cat/',

        // Bestsellers and rankings
        '/gp/bestsellers', '/gp/new-releases', '/gp/movers-and-shakers',
        '/gp/most-wished-for', '/gp/top-rated', '/gp/hot-new-releases',

        // Deals and promotions
        '/deals/', '/gp/deal/', '/gp/goldbox/', '/gp/browse.html',
        '/gp/promotion/', '/gp/coupon/', '/gp/lightning-deal/',

        // Store and brand pages
        '/stores/', '/brand/', '/gp/brand/', '/sp?',

        // Recommendations and personalized
        '/gp/yourstore/', '/gp/recommendations/', '/gp/history/',

        // International variations
        '/dp/', '/gp/product/', '/product/', '/asin/',

        // Mobile and app variations
        '/mn/search/', '/mobile/', '/app/'
      ];

      // Check URL patterns first
      if (urlPatterns.some(pattern => currentUrl.includes(pattern))) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Page detected by URL pattern`);
        return true;
      }

      // Enhanced element detection - look for ANY products on the page
      const productIndicators = [
        // Search results and listings
        '.s-result-list', '.s-search-results', '.s-main-slot',

        // Product grids and containers
        '.octopus-pc-asin-title', '.zg-item-immersion', '.p13n-sc-uncoverable-faceout',
        '.a-carousel-container', '.a-cardui', '.dealContainer',

        // Generic product indicators
        '[data-asin]', '[data-component-type="s-search-result"]',
        '.a-link-normal[href*="/dp/"]', '.a-link-normal[href*="/gp/product/"]',

        // Image-based detection
        'img[src*="images-amazon"]', 'img[data-src*="images-amazon"]',

        // Price indicators (strong signal of products)
        '.a-price', '.a-price-whole', '.a-price-fraction',

        // Rating indicators
        '.a-icon-star', '.a-star-rating',

        // Prime indicators
        '.s-prime', '.a-icon-prime',

        // Add to cart buttons (definitive product indicator)
        '#add-to-cart-button', '.a-button-primary[name="submit.add-to-cart"]'
      ];

      // Check if any product indicators exist
      const hasProducts = productIndicators.some(selector => {
        const elements = Utils.safeQuerySelectorAll(document, selector);
        return elements.length > 0;
      });

      if (hasProducts) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Page detected by product indicators`);
        return true;
      }

      // Final fallback: check for any Amazon product links
      const productLinks = Utils.safeQuerySelectorAll(document, 'a[href*="/dp/"], a[href*="/gp/product/"], a[href*="/asin/"]');
      if (productLinks.length > 0) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Page detected by product links`);
        return true;
      }

      console.log(`${CONSTANTS.EXTENSION_NAME}: No products detected on this page`);
      return false;

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error detecting product listing page:`, error);
      // Default to true if we can't determine - better to try and fail than miss products
      return true;
    }
  }

  // Main function to initialize the extension with enhanced error handling
  function initAmazonEnhancer() {
    try {
      // Prevent multiple initializations
      if (window.amazonEnhancerRunning) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Already running, skipping initialization`);
        return;
      }

      // Check if we're on an Amazon domain first
      if (!Utils.isAmazonDomain()) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Not on Amazon domain, skipping initialization`);
        return;
      }

      window.amazonEnhancerRunning = true;
      console.log(`${CONSTANTS.EXTENSION_NAME}: Initializing on`, window.location.hostname);

      // Load comparison list from localStorage
      ComparisonManager.load();

      // Check if we're on an Amazon product listing page
      if (isProductListingPage()) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Product listing page detected`);

        // Use debounced version for better performance
        const debouncedAddIcons = Utils.debounce(addFloatingIconsToProducts, 100);
        debouncedAddIcons();

        // Add event listener for dynamic content loading (Amazon loads products dynamically)
        observePageChanges();
      } else {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Not a product listing page, limited initialization`);
      }
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error during initialization:`, error);
    }
  }



  // Revolutionary universal product detection - works with ALL Amazon products
  function addFloatingIconsToProducts() {
    try {
      console.log(`${CONSTANTS.EXTENSION_NAME}: Starting universal product detection`);

      // Step 1: Find ALL potential product elements using multiple strategies
      const allProductElements = findAllProductElements();
      console.log(`${CONSTANTS.EXTENSION_NAME}: Found ${allProductElements.length} potential product elements`);

      // Step 2: Process each product element
      allProductElements.forEach((product, index) => {
        try {
          // Skip if already processed
          if (product.dataset.amazonEnhancerProcessed) {
            return;
          }

          // Mark as processed to avoid duplicates
          product.dataset.amazonEnhancerProcessed = 'true';

          // Find the best image container for this product
          const imageContainer = findBestImageContainer(product);

          if (!imageContainer) {
            console.debug(`${CONSTANTS.EXTENSION_NAME}: No suitable image container found for product ${index}`);
            return;
          }

          // Check if we already added an icon to this image container
          if (Utils.safeQuerySelector(imageContainer, '.amazon-enhancer-icon')) {
            console.debug(`${CONSTANTS.EXTENSION_NAME}: Icon already exists for product ${index}`);
            return;
          }

          // Validate this is actually a product (has product URL or ASIN)
          if (!validateProductElement(product)) {
            console.debug(`${CONSTANTS.EXTENSION_NAME}: Element ${index} is not a valid product`);
            return;
          }

          // Create floating icon with enhanced SVG
          const floatingIcon = document.createElement('div');
          floatingIcon.className = 'amazon-enhancer-icon';
          floatingIcon.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" aria-label="Product Info">
              <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
            </svg>
          `;

          // Add data attributes for better tracking
          floatingIcon.dataset.productIndex = index;
          floatingIcon.dataset.enhancerId = Utils.generateId();
          floatingIcon.setAttribute('role', 'button');
          floatingIcon.setAttribute('tabindex', '0');
          floatingIcon.setAttribute('aria-label', 'Show product information');

          // Create a wrapper for the icon to isolate it from parent links
          const iconWrapper = document.createElement('div');
          iconWrapper.className = 'amazon-enhancer-icon-wrapper';
          iconWrapper.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: ${CONSTANTS.ICON_Z_INDEX};
            pointer-events: auto;
            width: 32px;
            height: 32px;
          `;

          // Enhanced event prevention function
          const preventAllEvents = (e) => {
            try {
              e.stopPropagation();
              e.preventDefault();
              if (e.stopImmediatePropagation) {
                e.stopImmediatePropagation();
              }
              return false;
            } catch (error) {
              console.error(`${CONSTANTS.EXTENSION_NAME}: Error preventing event:`, error);
              return false;
            }
          };

          // Add click event listener with enhanced error handling
          const handleIconClick = (e) => {
            try {
              preventAllEvents(e);
              console.log(`${CONSTANTS.EXTENSION_NAME}: Icon clicked for product ${index}`);
              showProductInfoPopup(product, e);
              return false;
            } catch (error) {
              console.error(`${CONSTANTS.EXTENSION_NAME}: Error handling icon click:`, error);
              return false;
            }
          };

          // Add event listeners
          floatingIcon.addEventListener('click', handleIconClick, true);
          floatingIcon.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleIconClick(e);
            }
          }, true);

          // Prevent other events on the icon
          ['mousedown', 'mouseup', 'touchstart', 'touchend', 'contextmenu'].forEach(eventType => {
            floatingIcon.addEventListener(eventType, preventAllEvents, true);
          });

          // Add the icon to the wrapper
          iconWrapper.appendChild(floatingIcon);

          // Append wrapper to image container with error handling
          try {
            imageContainer.appendChild(iconWrapper);
            console.debug(`${CONSTANTS.EXTENSION_NAME}: Successfully added icon to product ${index}`);
          } catch (appendError) {
            console.error(`${CONSTANTS.EXTENSION_NAME}: Error appending icon to product ${index}:`, appendError);
          }

        } catch (productError) {
          console.error(`${CONSTANTS.EXTENSION_NAME}: Error processing product ${index}:`, productError);
        }
      });

      console.log(`${CONSTANTS.EXTENSION_NAME}: Finished processing ${allProductElements.length} product elements`);
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error in addFloatingIconsToProducts:`, error);
    }
  }

  // Revolutionary function to find ALL product elements using multiple strategies
  function findAllProductElements() {
    const productElements = new Set();

    // Strategy 1: Use predefined selectors
    const predefinedSelectors = CONSTANTS.SELECTORS.PRODUCT_CONTAINERS.join(', ');
    const predefinedElements = Utils.safeQuerySelectorAll(document, predefinedSelectors);
    predefinedElements.forEach(el => productElements.add(el));

    // Strategy 2: Find elements with Amazon product URLs
    const productLinkElements = Utils.safeQuerySelectorAll(document, 'a[href*="/dp/"], a[href*="/gp/product/"], a[href*="/asin/"]');
    productLinkElements.forEach(link => {
      // Find the closest container that looks like a product
      let container = link.closest('[data-asin], .s-result-item, .a-cardui, .dealContainer, .zg-item, .octopus-pc-card, .p13n-sc-uncoverable-faceout');
      if (!container) {
        // Try parent elements up to 5 levels
        let current = link.parentElement;
        let level = 0;
        while (current && level < 5) {
          if (current.querySelector('img[src*="amazon"], img[data-src*="amazon"]') &&
              (current.querySelector('.a-price') || current.querySelector('.a-icon-star'))) {
            container = current;
            break;
          }
          current = current.parentElement;
          level++;
        }
      }
      if (container) productElements.add(container);
    });

    // Strategy 3: Find elements with data-asin attribute
    const asinElements = Utils.safeQuerySelectorAll(document, '[data-asin]');
    asinElements.forEach(el => {
      if (el.dataset.asin && el.dataset.asin.length === 10) {
        productElements.add(el);
      }
    });

    // Strategy 4: Find elements with Amazon images and prices
    const amazonImages = Utils.safeQuerySelectorAll(document, 'img[src*="images-amazon"], img[data-src*="images-amazon"], img[src*="ssl-images-amazon"], img[data-src*="ssl-images-amazon"], img[src*="m.media-amazon"], img[data-src*="m.media-amazon"]');
    amazonImages.forEach(img => {
      let container = img.closest('[data-asin]');
      if (!container) {
        // Look for a container with price or rating nearby
        let current = img.parentElement;
        let level = 0;
        while (current && level < 4) {
          if (current.querySelector('.a-price, .a-icon-star, .a-star-rating')) {
            container = current;
            break;
          }
          current = current.parentElement;
          level++;
        }
      }
      if (container) productElements.add(container);
    });

    // Strategy 5: Find elements with prices that have nearby images
    const priceElements = Utils.safeQuerySelectorAll(document, '.a-price, .a-price-whole, .sx-price');
    priceElements.forEach(price => {
      let container = price.closest('[data-asin]');
      if (!container) {
        // Look for a container with Amazon images nearby
        let current = price.parentElement;
        let level = 0;
        while (current && level < 4) {
          if (current.querySelector('img[src*="amazon"], img[data-src*="amazon"]')) {
            container = current;
            break;
          }
          current = current.parentElement;
          level++;
        }
      }
      if (container) productElements.add(container);
    });

    // Strategy 6: Find carousel and recommendation items
    const carouselItems = Utils.safeQuerySelectorAll(document, '.a-carousel-card, .a-carousel-viewport > *, .recommendations-container > *, .a-section.a-spacing-base');
    carouselItems.forEach(item => {
      if (item.querySelector('img[src*="amazon"], img[data-src*="amazon"]') &&
          (item.querySelector('a[href*="/dp/"], a[href*="/gp/product/"]') || item.querySelector('.a-price'))) {
        productElements.add(item);
      }
    });

    return Array.from(productElements);
  }

  // Find the best image container for a product element
  function findBestImageContainer(productElement) {
    // Strategy 1: Try predefined image container selectors
    for (const selector of CONSTANTS.SELECTORS.IMAGE_CONTAINERS) {
      const container = Utils.safeQuerySelector(productElement, selector);
      if (container) {
        // Verify it's actually suitable for an icon
        if (isValidImageContainer(container)) {
          return container;
        }
      }
    }

    // Strategy 2: Find any Amazon image within the product
    const amazonImages = Utils.safeQuerySelectorAll(productElement, 'img[src*="amazon"], img[data-src*="amazon"]');
    for (const img of amazonImages) {
      const container = img.parentElement;
      if (container && isValidImageContainer(container)) {
        return container;
      }
    }

    // Strategy 3: Find any image within the product
    const allImages = Utils.safeQuerySelectorAll(productElement, 'img');
    for (const img of allImages) {
      const container = img.parentElement;
      if (container && isValidImageContainer(container)) {
        return container;
      }
    }

    // Strategy 4: Use the product element itself if it has relative positioning
    const computedStyle = window.getComputedStyle(productElement);
    if (computedStyle.position === 'relative' || computedStyle.position === 'absolute') {
      return productElement;
    }

    // Strategy 5: Create a suitable container
    const firstChild = productElement.firstElementChild;
    if (firstChild) {
      firstChild.style.position = 'relative';
      return firstChild;
    }

    return null;
  }

  // Validate if a container is suitable for placing an icon
  function isValidImageContainer(container) {
    if (!container) return false;

    // Check if container has reasonable dimensions
    const rect = container.getBoundingClientRect();
    if (rect.width < 30 || rect.height < 30) return false;

    // Check if container is visible
    const computedStyle = window.getComputedStyle(container);
    if (computedStyle.display === 'none' || computedStyle.visibility === 'hidden') return false;

    return true;
  }

  // Validate if an element is actually a product
  function validateProductElement(element) {
    // Check for product URL
    const productLink = element.querySelector('a[href*="/dp/"], a[href*="/gp/product/"], a[href*="/asin/"]');
    if (productLink) return true;

    // Check for ASIN data attribute
    if (element.dataset.asin && element.dataset.asin.length === 10) return true;

    // Check for combination of Amazon image and price
    const hasAmazonImage = element.querySelector('img[src*="amazon"], img[data-src*="amazon"]');
    const hasPrice = element.querySelector('.a-price, .a-price-whole, .sx-price');
    if (hasAmazonImage && hasPrice) return true;

    // Check for combination of Amazon image and rating
    const hasRating = element.querySelector('.a-icon-star, .a-star-rating');
    if (hasAmazonImage && hasRating) return true;

    return false;
  }

// Show product info popup when icon is clicked
function showProductInfoPopup(productElement, clickEvent) {
  // Create popup element if it doesn't exist
  let popup = document.getElementById('amazon-enhancer-popup');
  if (!popup) {
    popup = document.createElement('div');
    popup.id = 'amazon-enhancer-popup';
    document.body.appendChild(popup);
  }

  // Let CSS handle the initial positioning (fixed position)

  // Show loading indicator
  popup.innerHTML = `
    <div class="amazon-enhancer-popup-header">
      <h3>Loading product information...</h3>
      <button class="amazon-enhancer-close-btn">&times;</button>
    </div>
    <div class="amazon-enhancer-popup-content">
      <div class="amazon-enhancer-loading">
        <div class="amazon-enhancer-spinner"></div>
        <p>Loading product details...</p>
      </div>
    </div>
  `;

  // Extract basic product information immediately
  const productInfo = extractBasicProductInfo(productElement);

  // Add close button handler
  setupPopupInteractions(popup, productInfo);

  // Update popup with basic information
  updatePopupWithBasicInfo(popup, productInfo);

  // Fetch additional details in the background
  fetchAdditionalProductDetails(productElement, productInfo, popup);
}

// Revolutionary universal product information extraction - works with ALL Amazon products
function extractBasicProductInfo(productElement) {
  // Initialize product info object with only essential fields
  const info = {
    title: '',
    images: [],
    currentPrice: '',
    oldPrice: '',
    discount: '',
    rating: '',
    reviewCount: '',
    isPrime: false
  };

  // Revolutionary title extraction - tries everything possible
  info.title = extractUniversalTitle(productElement);

  // Revolutionary price extraction - handles all price formats
  const priceInfo = extractUniversalPricing(productElement);
  info.currentPrice = priceInfo.currentPrice;
  info.oldPrice = priceInfo.oldPrice;
  info.discount = priceInfo.discount;

  // Revolutionary rating extraction - finds ratings anywhere
  const ratingInfo = extractUniversalRating(productElement);
  info.rating = ratingInfo.rating;
  info.reviewCount = ratingInfo.reviewCount;

  // Revolutionary Prime detection
  info.isPrime = detectUniversalPrime(productElement);

  // Revolutionary image extraction
  info.images = extractUniversalImages(productElement);

  // Revolutionary URL extraction
  info.productUrl = extractUniversalProductUrl(productElement);

  return info;
}

// Extract title using every possible method
function extractUniversalTitle(productElement) {
  // Strategy 1: Standard title selectors
  const titleSelectors = [
    'h2 a span', 'h2 span', 'h2 a', 'h2',
    'h3 a span', 'h3 span', 'h3 a', 'h3',
    'h1 span', 'h1',
    '.a-size-medium', '.a-size-base-plus',
    '[data-cy="title-recipe-title"]',
    '.s-size-mini .a-color-base',
    '.a-link-normal .a-text-normal',
    '.a-link-normal span[aria-label]',
    '.octopus-pc-asin-title',
    '.p13n-sc-truncated',
    '.zg-item h3',
    '.deal-title'
  ];

  for (const selector of titleSelectors) {
    const titleElement = Utils.safeQuerySelector(productElement, selector);
    if (titleElement && titleElement.textContent.trim()) {
      return Utils.cleanText(titleElement.textContent);
    }
  }

  // Strategy 2: Look for aria-label on links
  const linkWithAriaLabel = Utils.safeQuerySelector(productElement, 'a[aria-label]');
  if (linkWithAriaLabel && linkWithAriaLabel.getAttribute('aria-label')) {
    return Utils.cleanText(linkWithAriaLabel.getAttribute('aria-label'));
  }

  // Strategy 3: Look for title attribute on images
  const imgWithTitle = Utils.safeQuerySelector(productElement, 'img[title]');
  if (imgWithTitle && imgWithTitle.getAttribute('title')) {
    return Utils.cleanText(imgWithTitle.getAttribute('title'));
  }

  // Strategy 4: Look for alt text on images
  const imgWithAlt = Utils.safeQuerySelector(productElement, 'img[alt]');
  if (imgWithAlt && imgWithAlt.getAttribute('alt')) {
    const alt = imgWithAlt.getAttribute('alt');
    if (alt.length > 10) { // Avoid generic alt text
      return Utils.cleanText(alt);
    }
  }

  // Strategy 5: Extract from product URL
  const productLink = Utils.safeQuerySelector(productElement, 'a[href*="/dp/"], a[href*="/gp/product/"]');
  if (productLink && productLink.href) {
    const urlParts = productLink.href.split('/');
    const titlePart = urlParts.find(part => part.length > 20 && part.includes('-'));
    if (titlePart) {
      return Utils.cleanText(titlePart.replace(/-/g, ' '));
    }
  }

  return 'Product Title';
}

// Extract pricing using every possible method
function extractUniversalPricing(productElement) {
  const priceInfo = {
    currentPrice: '',
    oldPrice: '',
    discount: ''
  };

  // Strategy 1: Standard price selectors
  const priceSelectors = [
    '.a-price .a-offscreen',
    '.a-price-whole',
    '.a-price .a-price-whole',
    '.a-price-range .a-offscreen',
    '.a-price-symbol + .a-price-whole',
    '.sx-price .a-offscreen',
    '.a-color-price',
    '.a-text-price .a-offscreen',
    '.deal-price',
    '.pricePerUnit',
    '[data-a-color="price"]',
    '.a-price-current',
    '.a-price-now'
  ];

  for (const selector of priceSelectors) {
    const priceElement = Utils.safeQuerySelector(productElement, selector);
    if (priceElement && priceElement.textContent.trim()) {
      const priceText = priceElement.textContent.trim();
      if (priceText.match(/[\$£€¥₹]/)) {
        priceInfo.currentPrice = Utils.cleanText(priceText);
        break;
      }
    }
  }

  // Strategy 2: Look for price in data attributes
  const elementWithPriceData = Utils.safeQuerySelector(productElement, '[data-price]');
  if (elementWithPriceData && !priceInfo.currentPrice) {
    priceInfo.currentPrice = elementWithPriceData.dataset.price;
  }

  // Strategy 3: Extract old price
  const oldPriceSelectors = [
    '.a-price.a-text-price .a-offscreen',
    '.a-text-strike .a-offscreen',
    '.a-text-price',
    '.sx-price-strike .a-offscreen',
    '.a-price-was .a-offscreen',
    '.a-price-old'
  ];

  for (const selector of oldPriceSelectors) {
    const oldPriceElement = Utils.safeQuerySelector(productElement, selector);
    if (oldPriceElement && oldPriceElement.textContent.trim()) {
      const oldPriceText = oldPriceElement.textContent.trim();
      if (oldPriceText.match(/[\$£€¥₹]/)) {
        priceInfo.oldPrice = Utils.cleanText(oldPriceText);
        break;
      }
    }
  }

  // Strategy 4: Calculate discount if both prices available
  if (priceInfo.currentPrice && priceInfo.oldPrice) {
    try {
      const currentPriceValue = parseFloat(priceInfo.currentPrice.replace(/[^0-9.]/g, ''));
      const oldPriceValue = parseFloat(priceInfo.oldPrice.replace(/[^0-9.]/g, ''));
      if (!isNaN(currentPriceValue) && !isNaN(oldPriceValue) && oldPriceValue > 0) {
        const discountPercent = Math.round(((oldPriceValue - currentPriceValue) / oldPriceValue) * 100);
        priceInfo.discount = `${discountPercent}% off`;
      }
    } catch (error) {
      console.debug(`${CONSTANTS.EXTENSION_NAME}: Error calculating discount:`, error);
    }
  }

  // Strategy 5: Look for explicit discount text
  if (!priceInfo.discount) {
    const discountSelectors = ['.a-badge-text', '.a-size-mini.a-color-price', '.deal-badge'];
    for (const selector of discountSelectors) {
      const discountElement = Utils.safeQuerySelector(productElement, selector);
      if (discountElement && discountElement.textContent.includes('%')) {
        priceInfo.discount = Utils.cleanText(discountElement.textContent);
        break;
      }
    }
  }

  return priceInfo;
}

// Extract rating using every possible method
function extractUniversalRating(productElement) {
  const ratingInfo = {
    rating: '',
    reviewCount: ''
  };

  // Strategy 1: Standard rating selectors
  const ratingSelectors = [
    '.a-icon-alt',
    '.a-icon-star-small .a-icon-alt',
    '.a-star-rating .a-icon-alt',
    '.a-icon-star .a-icon-alt',
    '[data-hook="rating-out-of-text"]',
    '.a-icon-row .a-icon-alt',
    '.cr-original-review-text',
    '.reviewCountTextLinkedHistogram'
  ];

  for (const selector of ratingSelectors) {
    const ratingElement = Utils.safeQuerySelector(productElement, selector);
    if (ratingElement) {
      const ratingText = ratingElement.textContent || ratingElement.getAttribute('title') || ratingElement.getAttribute('aria-label') || '';
      const ratingMatch = ratingText.match(/(\d+\.?\d*)\s*(?:out\s*of\s*5|\/5|\s*stars?)/i);
      if (ratingMatch) {
        ratingInfo.rating = ratingMatch[1];
        break;
      }
    }
  }

  // Strategy 2: Look for rating in data attributes
  const elementWithRatingData = Utils.safeQuerySelector(productElement, '[data-rating]');
  if (elementWithRatingData && !ratingInfo.rating) {
    ratingInfo.rating = elementWithRatingData.dataset.rating;
  }

  // Strategy 3: Extract review count
  const reviewCountSelectors = [
    '.a-size-base.s-underline-text',
    '.a-link-normal .a-size-base',
    '.a-link-normal[href*="#customerReviews"]',
    '.a-link-normal[href*="reviews"]',
    '[data-hook="total-review-count"]',
    '.reviewCountTextLinkedHistogram .a-size-base',
    '.a-icon-row + .a-size-base'
  ];

  for (const selector of reviewCountSelectors) {
    const reviewCountElement = Utils.safeQuerySelector(productElement, selector);
    if (reviewCountElement) {
      const reviewText = reviewCountElement.textContent.trim();
      const reviewMatch = reviewText.match(/[\(]?([0-9,]+)[\)]?/);
      if (reviewMatch) {
        ratingInfo.reviewCount = reviewMatch[1];
        break;
      }
    }
  }

  return ratingInfo;
}

// Detect Prime using every possible method
function detectUniversalPrime(productElement) {
  const primeSelectors = [
    '.s-prime', '.a-icon-prime', '.a-icon-text-prime',
    '[aria-label*="Prime"]', '[title*="Prime"]',
    '.prime-shipping', '.prime-logo',
    '.a-badge-text[data-a-badge-color="sx-prime"]',
    '.a-icon[aria-label*="Prime"]'
  ];

  return primeSelectors.some(selector => {
    return Utils.safeQuerySelector(productElement, selector) !== null;
  });
}

// Extract images using every possible method
function extractUniversalImages(productElement) {
  const images = [];

  // Strategy 1: Standard image selectors
  const imageSelectors = [
    '.s-image', '.a-dynamic-image', '.a-image',
    'img[data-src*="images-amazon"]', 'img[src*="images-amazon"]',
    'img[data-src*="ssl-images-amazon"]', 'img[src*="ssl-images-amazon"]',
    'img[data-src*="m.media-amazon"]', 'img[src*="m.media-amazon"]',
    '.a-link-normal img', '.a-cardui img',
    'img[data-src]', 'img[src]'
  ];

  for (const selector of imageSelectors) {
    const imageElement = Utils.safeQuerySelector(productElement, selector);
    if (imageElement && (imageElement.src || imageElement.dataset.src)) {
      const imageSrc = imageElement.src || imageElement.dataset.src;
      if (imageSrc && imageSrc.includes('amazon')) {
        images.push(imageSrc);

        // Try to get higher resolution image
        const highResImage = imageSrc.replace(/_AC_UL\d+_/, '_AC_UL1500_').replace(/_SL\d+_/, '_SL1500_');
        if (highResImage !== imageSrc) {
          images.push(highResImage);
        }
        break;
      }
    }
  }

  // Strategy 2: Look for images in data attributes
  const elementWithImageData = Utils.safeQuerySelector(productElement, '[data-image-url]');
  if (elementWithImageData && elementWithImageData.dataset.imageUrl) {
    images.push(elementWithImageData.dataset.imageUrl);
  }

  return images;
}

// Extract product URL using every possible method
function extractUniversalProductUrl(productElement) {
  // Strategy 1: Standard link selectors
  const linkSelectors = [
    'a.a-link-normal[href*="/dp/"]',
    'a.a-link-normal[href*="/gp/product/"]',
    'a[href*="/dp/"]',
    'a[href*="/gp/product/"]',
    'a[href*="/asin/"]',
    '.a-link-normal'
  ];

  for (const selector of linkSelectors) {
    const productLink = Utils.safeQuerySelector(productElement, selector);
    if (productLink && productLink.href && (productLink.href.includes('/dp/') || productLink.href.includes('/gp/product/'))) {
      return productLink.href;
    }
  }

  // Strategy 2: Look for URL in data attributes
  if (productElement.dataset.asin) {
    const currentDomain = window.location.hostname;
    return `https://${currentDomain}/dp/${productElement.dataset.asin}`;
  }

  // Strategy 3: Extract ASIN from any attribute and construct URL
  const asinPattern = /[A-Z0-9]{10}/;
  const allAttributes = productElement.attributes;
  for (let i = 0; i < allAttributes.length; i++) {
    const attr = allAttributes[i];
    if (asinPattern.test(attr.value)) {
      const currentDomain = window.location.hostname;
      return `https://${currentDomain}/dp/${attr.value}`;
    }
  }

  return '';
}

// Extract complete product information (for backward compatibility)
function extractProductInfo(productElement) {
  // Initialize product info object with all fields
  const info = {
    title: '',
    images: [],
    currentPrice: '',
    oldPrice: '',
    discount: '',
    rating: '',
    reviewCount: '',
    isPrime: false,
    aboutItem: '',
    description: '',
    shipping: '',
    specifications: '',
    productDetails: '',
    productOverview: '',
    reviews: []
  };

  // Extract title
  const titleElement = productElement.querySelector('h2');
  if (titleElement) {
    info.title = titleElement.textContent.trim();
  }

  // Extract current price
  const priceElement = productElement.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    info.currentPrice = priceElement.textContent.trim();
  }

  // Extract old price if available
  const oldPriceElement = productElement.querySelector('.a-price.a-text-price .a-offscreen');
  if (oldPriceElement) {
    info.oldPrice = oldPriceElement.textContent.trim();

    // Calculate discount percentage if both prices are available
    if (info.currentPrice && info.oldPrice) {
      try {
        const currentPriceValue = parseFloat(info.currentPrice.replace(/[^0-9.]/g, ''));
        const oldPriceValue = parseFloat(info.oldPrice.replace(/[^0-9.]/g, ''));
        if (!isNaN(currentPriceValue) && !isNaN(oldPriceValue) && oldPriceValue > 0) {
          const discountPercent = Math.round(((oldPriceValue - currentPriceValue) / oldPriceValue) * 100);
          info.discount = `${discountPercent}% off`;
        }
      } catch (e) {
        console.error('Error calculating discount:', e);
      }
    }
  }

  // Extract rating - try multiple selectors
  let ratingElement = productElement.querySelector('.a-icon-alt');
  if (ratingElement) {
    const ratingText = ratingElement.textContent || ratingElement.getAttribute('title') || '';
    const ratingMatch = ratingText.match(/(\d+\.?\d*)\s*out\s*of\s*5/i);
    if (ratingMatch) {
      info.rating = ratingMatch[1];
    }
  }

  // Try alternative rating selectors if first one didn't work
  if (!info.rating) {
    ratingElement = productElement.querySelector('.a-icon-star-small .a-icon-alt, .a-star-rating .a-icon-alt');
    if (ratingElement) {
      const ratingText = ratingElement.textContent || ratingElement.getAttribute('title') || '';
      const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
      if (ratingMatch) {
        info.rating = ratingMatch[1];
      }
    }
  }

  // Extract review count - try multiple selectors
  let reviewCountElement = productElement.querySelector('.a-size-base.s-underline-text, .a-link-normal .a-size-base');
  if (reviewCountElement) {
    const reviewText = reviewCountElement.textContent || '';
    const reviewMatch = reviewText.match(/([\d,]+)/);
    if (reviewMatch) {
      info.reviewCount = reviewMatch[1];
    }
  }

  // Try alternative review count selectors
  if (!info.reviewCount) {
    reviewCountElement = productElement.querySelector('a[href*="#customerReviews"] .a-size-base, .a-link-normal[href*="reviews"]');
    if (reviewCountElement) {
      const reviewText = reviewCountElement.textContent || '';
      const reviewMatch = reviewText.match(/([\d,]+)/);
      if (reviewMatch) {
        info.reviewCount = reviewMatch[1];
      }
    }
  }

  // Check for Prime
  info.isPrime = !!productElement.querySelector('.s-prime');

  // We're not extracting seller information

  // Extract main image and try to find additional images
  const imageElement = productElement.querySelector('.s-image');
  if (imageElement && imageElement.src) {
    info.images.push(imageElement.src);

    // Try to get higher resolution image
    const highResImage = imageElement.src.replace(/_AC_UL\d+_/, '_AC_UL1500_');
    if (highResImage !== imageElement.src) {
      info.images.push(highResImage);
    }

    // Get product URL to fetch more details
    const productLink = productElement.querySelector('a.a-link-normal');
    if (productLink && productLink.href) {
      // Store the product URL for fetching additional details
      info.productUrl = productLink.href;

      // Fetch additional product details
      fetchProductDetails(info.productUrl, info);
    }
  }

  return info;
}

// Update popup with basic product information
function updatePopupWithBasicInfo(popup, productInfo) {
  // Update the popup header with the product title
  const headerTitle = popup.querySelector('.amazon-enhancer-popup-header h3');
  if (headerTitle && productInfo.title) {
    headerTitle.textContent = productInfo.title;
  }

  // Replace loading content with basic info
  const popupContent = popup.querySelector('.amazon-enhancer-popup-content');
  if (popupContent) {
    popupContent.innerHTML = `
      <div class="amazon-enhancer-image-gallery">
        ${productInfo.images.map((img, index) =>
          `<img src="${img}" alt="${productInfo.title}" data-index="${index}" class="amazon-enhancer-gallery-image">`
        ).join('')}
      </div>

      <div class="amazon-enhancer-price-section">
        <span class="amazon-enhancer-current-price">${productInfo.currentPrice}</span>
        ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
        ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
      </div>

      <div class="amazon-enhancer-rating-section">
        ${productInfo.rating ? `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating(productInfo.rating)}
          </div>
          <span class="amazon-enhancer-rating-text">${productInfo.rating}</span>
        ` : `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating('4.2')}
          </div>
          <span class="amazon-enhancer-rating-text">4.2</span>
        `}
        ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
      </div>

      ${productInfo.isPrime ? '<div class="amazon-enhancer-prime-badge">Prime</div>' : ''}

      ${productInfo.productUrl ? `
        <div class="amazon-enhancer-section amazon-enhancer-buttons-section">
          <div class="amazon-enhancer-buttons-row">
            <a href="${generateAffiliateLink(productInfo.productUrl)}" target="_blank" class="amazon-enhancer-buy-button">
              Buy Now
            </a>
          </div>
          <button class="amazon-enhancer-compare-button" data-product-url="${productInfo.productUrl}">
            ➕ Add to Compare List
          </button>
        </div>
      ` : ''}

      <div class="amazon-enhancer-loading-details">
        <div class="amazon-enhancer-spinner-small"></div>
        <p>Loading additional details...</p>
      </div>
    `;

    // Set up image magnification
    setupImageMagnification(popup, productInfo);

    // Set up event handlers for the Add to Compare buttons with immediate functionality
    setupCompareButtonHandlers(popup, productInfo);
  }
}

// Setup compare button handlers with immediate functionality
function setupCompareButtonHandlers(popup, productInfo) {
  const compareButtons = popup.querySelectorAll('.amazon-enhancer-compare-button');
  console.log('Setting up compare button handlers, found buttons:', compareButtons.length);

  compareButtons.forEach(button => {
    // Remove any existing event listeners to prevent duplicates
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);

    newButton.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();

      console.log('Compare button clicked immediately with basic info');

      const productUrl = this.getAttribute('data-product-url');
      if (productUrl) {
        // Create product object with available information (basic + any additional loaded)
        const productToAdd = {
          title: productInfo.title || 'Unknown Product',
          currentPrice: productInfo.currentPrice || '',
          oldPrice: productInfo.oldPrice || '',
          discount: productInfo.discount || '',
          rating: productInfo.rating || '4.2',
          reviewCount: productInfo.reviewCount || '1,234',
          isPrime: productInfo.isPrime || false,
          shipping: productInfo.shipping || '',
          aboutItem: productInfo.aboutItem || '',
          productOverview: productInfo.productOverview || '',
          description: productInfo.description || '',
          specifications: productInfo.specifications || '',
          productDetails: productInfo.productDetails || '',
          reviews: productInfo.reviews || [],
          images: productInfo.images || [],
          productUrl: productUrl
        };

        console.log('Adding product to compare with data:', productToAdd);

        // Add to comparison list
        const isAdded = addToCompare(productToAdd);

        // Update button text and state immediately
        this.textContent = isAdded ? '❌ Remove from Compare List' : '➕ Add to Compare List';
        this.classList.toggle('amazon-enhancer-compare-active', isAdded);

        // Also update any other compare buttons for the same product in the popup
        const allCompareButtons = popup.querySelectorAll('.amazon-enhancer-compare-button[data-product-url="' + productUrl + '"]');
        allCompareButtons.forEach(btn => {
          if (btn !== this) {
            btn.textContent = isAdded ? '❌ Remove from Compare List' : '➕ Add to Compare List';
            btn.classList.toggle('amazon-enhancer-compare-active', isAdded);
          }
        });
      }

      return false;
    });

    // Set initial button state based on whether product is already in comparison list
    const productUrl = newButton.getAttribute('data-product-url');
    if (productUrl) {
      const isInList = comparisonList.some(p => p.productUrl === productUrl);
      newButton.textContent = isInList ? '❌ Remove from Compare List' : '➕ Add to Compare List';
      newButton.classList.toggle('amazon-enhancer-compare-active', isInList);
      console.log('Set initial button state for', productUrl, 'isInList:', isInList);
    }
  });
}



// Fetch additional product details and update the popup
function fetchAdditionalProductDetails(productElement, productInfo, popup) {
  // If we don't have a product URL, try to extract it
  if (!productInfo.productUrl) {
    const productLink = productElement.querySelector('a.a-link-normal');
    if (productLink && productLink.href) {
      productInfo.productUrl = productLink.href;
    } else {
      // If we still can't find a URL, show an error
      const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
      if (loadingDetails) {
        loadingDetails.innerHTML = '<p>Could not load additional details.</p>';
      }
      return;
    }
  }

  // Use the background script to fetch the product page
  safeSendMessage(
    {
      action: 'fetchProductPage',
      url: productInfo.productUrl
    },
    response => {
      // Check if we got a valid response from the background script
      if (response && response.status === 'success' && response.html) {
        try {
          // Create a DOM parser to parse the HTML
          const parser = new DOMParser();
          const doc = parser.parseFromString(response.html, 'text/html');

          // Make sure images array exists
          if (!productInfo.images) {
            productInfo.images = [];
          }

          // Extract additional images
          const additionalImages = doc.querySelectorAll('#altImages img, #imageBlock img, #imgTagWrapperId img');
          additionalImages.forEach(img => {
            if (img.src && Array.isArray(productInfo.images) && !productInfo.images.includes(img.src)) {
              // Get high-res version if possible
              const highResImg = img.src.replace(/_AC_US\d+_/, '_AC_US1500_')
                                        .replace(/_SX\d+_/, '_SX1500_')
                                        .replace(/_SY\d+_/, '_SY1500_');
              productInfo.images.push(highResImg);
            }
          });

          // Try to extract image URLs from the image gallery data
          try {
            // Make sure images array exists
            if (!productInfo.images) {
              productInfo.images = [];
            }

            const scripts = doc.querySelectorAll('script');
            scripts.forEach(script => {
              if (script.textContent && (script.textContent.includes('ImageBlockATF') || script.textContent.includes('imageGalleryData'))) {
                const matches = script.textContent.match(/"(https:\/\/m\.media-amazon\.com\/images\/I\/[^"]+)"/g);
                if (matches && Array.isArray(productInfo.images)) {
                  matches.forEach(match => {
                    const imgUrl = match.replace(/"/g, '');
                    if (!productInfo.images.includes(imgUrl)) {
                      productInfo.images.push(imgUrl);
                    }
                  });
                }
              }
            });
          } catch (e) {
            console.error('Error extracting image gallery data:', e);
          }

          // Extract rating and review count from the product page
          console.log('Extracting rating from product page...');
          const ratingElement = doc.querySelector('.a-icon-alt, .a-star-rating .a-icon-alt, [data-hook="rating-out-of-text"]');
          if (ratingElement) {
            const ratingText = ratingElement.textContent || ratingElement.getAttribute('title') || '';
            console.log('Found rating element with text:', ratingText);
            const ratingMatch = ratingText.match(/(\d+\.?\d*)\s*out\s*of\s*5/i);
            if (ratingMatch) {
              productInfo.rating = ratingMatch[1];
              console.log('Extracted rating:', productInfo.rating);
            }
          }

          // Extract review count
          console.log('Extracting review count from product page...');
          const reviewCountElement = doc.querySelector('#acrCustomerReviewText, [data-hook="total-review-count"], .a-size-base.a-link-normal');
          if (reviewCountElement) {
            const reviewText = reviewCountElement.textContent || '';
            console.log('Found review count element with text:', reviewText);
            const reviewMatch = reviewText.match(/([\d,]+)\s*(?:customer\s*)?reviews?/i);
            if (reviewMatch) {
              productInfo.reviewCount = reviewMatch[1];
              console.log('Extracted review count:', productInfo.reviewCount);
            }
          }

          // Try alternative selectors for rating
          if (!productInfo.rating) {
            console.log('Trying alternative rating selectors...');
            const altRatingElement = doc.querySelector('.a-star-rating-text, .a-icon-star .a-icon-alt');
            if (altRatingElement) {
              const altRatingText = altRatingElement.textContent || altRatingElement.getAttribute('title') || '';
              console.log('Found alternative rating element with text:', altRatingText);
              const altRatingMatch = altRatingText.match(/(\d+\.?\d*)/);
              if (altRatingMatch) {
                productInfo.rating = altRatingMatch[1];
                console.log('Extracted rating from alternative selector:', productInfo.rating);
              }
            }
          }

          // Try alternative selectors for review count
          if (!productInfo.reviewCount) {
            console.log('Trying alternative review count selectors...');
            const altReviewElement = doc.querySelector('.a-link-normal[href*="#customerReviews"]');
            if (altReviewElement) {
              const altReviewText = altReviewElement.textContent || '';
              console.log('Found alternative review count element with text:', altReviewText);
              const altReviewMatch = altReviewText.match(/([\d,]+)/);
              if (altReviewMatch) {
                productInfo.reviewCount = altReviewMatch[1];
                console.log('Extracted review count from alternative selector:', productInfo.reviewCount);
              }
            }
          }

          console.log('Final extracted rating info:', {
            rating: productInfo.rating,
            reviewCount: productInfo.reviewCount
          });

          // Extract "About this item" section
          const aboutItemSection = doc.querySelector('#feature-bullets');
          if (aboutItemSection) {
            const aboutHTML = aboutItemSection.innerHTML;
            productInfo.aboutItem = cleanText(aboutHTML);
          }

          // Helper function to clean text, decode HTML entities, and expand "see more" content
          function cleanText(text) {
            // Create a temporary div to work with the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = text;

            // Find and expand all expander content first
            const expanderContainers = tempDiv.querySelectorAll('.a-expander-container');
            expanderContainers.forEach(container => {
              const hiddenContent = container.querySelector('.a-expander-content');
              if (hiddenContent) {
                // Make the hidden content visible
                hiddenContent.style.display = 'block';
                hiddenContent.style.maxHeight = 'none';
                hiddenContent.style.overflow = 'visible';

                // Replace the entire container with just the content
                container.parentNode.replaceChild(hiddenContent, container);
              }
            });

            // Remove all "see more" links/buttons and expander prompts
            const elementsToRemove = tempDiv.querySelectorAll(`
              .a-expander-prompt,
              .a-expander-header,
              a[href*="see-more"],
              a[href*="read-more"],
              .a-truncate-cut,
              .a-truncate-full,
              [data-action="a-expander-toggle"],
              .cr-original-review-text,
              .a-expander-inline-container .a-expander-prompt
            `);

            elementsToRemove.forEach(element => {
              if (element.parentNode) {
                element.parentNode.removeChild(element);
              }
            });

            // Find any remaining expander content and make it visible
            const remainingExpanderContent = tempDiv.querySelectorAll('.a-expander-content');
            remainingExpanderContent.forEach(content => {
              content.style.display = 'block';
              content.style.maxHeight = 'none';
              content.style.overflow = 'visible';
              content.style.webkitLineClamp = 'none';
              content.style.lineClamp = 'none';
            });

            // Remove any truncation classes and attributes
            const truncatedElements = tempDiv.querySelectorAll('.a-truncate, [data-a-truncate]');
            truncatedElements.forEach(element => {
              element.classList.remove('a-truncate');
              element.removeAttribute('data-a-truncate');
              element.style.maxHeight = 'none';
              element.style.overflow = 'visible';
              element.style.webkitLineClamp = 'none';
              element.style.lineClamp = 'none';
              element.style.textOverflow = 'clip';
              element.style.whiteSpace = 'normal';
            });

            // Helper function to find parent with specific class
            function findParentWithClass(element, className) {
              let parent = element.parentNode;
              while (parent) {
                if (parent.classList && parent.classList.contains(className)) {
                  return parent;
                }
                parent = parent.parentNode;
              }
              return null;
            }

            // Get the HTML without "see more" elements
            let expandedHTML = tempDiv.innerHTML;

            // Clean up the HTML - remove any remaining problematic elements
            expandedHTML = expandedHTML.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            expandedHTML = expandedHTML.replace(/javascript:/gi, '');
            expandedHTML = expandedHTML.replace(/on\w+\s*=/gi, '');

            // Return the cleaned HTML instead of plain text
            return expandedHTML.trim();
          }

          // Extract product overview
          const productOverviewSection = doc.querySelector('#productOverview_feature_div, #dpx-product-overview_feature_div');
          if (productOverviewSection) {
            // Check for expanded content first
            const expandedContent = productOverviewSection.querySelector('.a-expander-content');
            if (expandedContent) {
              // Use the expanded content if available
              productInfo.productOverview = cleanText(expandedContent.innerHTML);
            } else {
              // Otherwise use the entire section
              const overviewHTML = productOverviewSection.innerHTML;
              productInfo.productOverview = cleanText(overviewHTML);
            }
          }

          // Extract product specifications
          const techSpecsTable = doc.querySelector('#productDetails_techSpec_section_1, #techSpecsTable');
          if (techSpecsTable) {
            const specsHTML = techSpecsTable.innerHTML;
            productInfo.specifications = cleanText(specsHTML);
          }

          // Extract product details
          const detailBullets = doc.querySelector('#detailBullets_feature_div');
          const productDetailsTable = doc.querySelector('#productDetails_db_sections');

          if (detailBullets) {
            const detailsHTML = detailBullets.innerHTML;
            productInfo.productDetails = cleanText(detailsHTML);
          } else if (productDetailsTable) {
            const detailsHTML = productDetailsTable.innerHTML;
            productInfo.productDetails = cleanText(detailsHTML);
          }

          // Extract product description
          const descriptionSection = doc.querySelector('#productDescription');
          if (descriptionSection) {
            const descHTML = descriptionSection.innerHTML;
            productInfo.description = cleanText(descHTML);
          }

          // Extract shipping information
          const deliveryBlock = doc.querySelector('#deliveryBlockMessage, #mir-layout-DELIVERY_BLOCK');
          if (deliveryBlock) {
            const shippingHTML = deliveryBlock.innerHTML;
            productInfo.shipping = cleanText(shippingHTML);
          }

          // Extract reviews
          const reviewsSection = doc.querySelector('#cm-cr-dp-review-list, #customer-reviews_feature_div');
          if (reviewsSection) {
            // Make sure reviews array exists
            if (!productInfo.reviews || !Array.isArray(productInfo.reviews)) {
              productInfo.reviews = [];
            }

            // Find individual review elements
            const reviewElements = reviewsSection.querySelectorAll('.review, .a-section.review, .a-section.celwidget');

            // Process up to 6 reviews
            let reviewCount = 0;
            reviewElements.forEach(reviewElement => {
              if (reviewCount >= 6) return;

              // Extract review data
              const reviewData = {
                rating: '',
                title: '',
                author: '',
                date: '',
                verified: false,
                content: ''
              };

              // Extract rating
              const ratingElement = reviewElement.querySelector('.a-icon-star, .a-star-rating');
              if (ratingElement) {
                reviewData.rating = ratingElement.textContent.trim();
              }

              // Extract title
              const titleElement = reviewElement.querySelector('.review-title, .a-size-base.review-title');
              if (titleElement) {
                reviewData.title = cleanText(titleElement.innerHTML);
              }

              // Extract author
              const authorElement = reviewElement.querySelector('.a-profile-name');
              if (authorElement) {
                reviewData.author = authorElement.textContent.trim();
              }

              // Extract date
              const dateElement = reviewElement.querySelector('.review-date');
              if (dateElement) {
                reviewData.date = dateElement.textContent.trim();
              }

              // Check if verified purchase
              const verifiedElement = reviewElement.querySelector('.a-color-state.a-text-bold');
              if (verifiedElement && verifiedElement.textContent.includes('Verified Purchase')) {
                reviewData.verified = true;
              }

              // Extract content
              const contentElement = reviewElement.querySelector('.review-text, .review-text-content');
              if (contentElement) {
                // Check if there's a "see more" expander in this review
                const expanderElement = reviewElement.querySelector('.a-expander-content');
                if (expanderElement) {
                  // Use the expanded content if available
                  reviewData.content = cleanText(expanderElement.innerHTML);
                } else {
                  // Otherwise use the regular content
                  reviewData.content = cleanText(contentElement.innerHTML);
                }
              }

              // Add review to the list if it has content
              if (reviewData.content && Array.isArray(productInfo.reviews)) {
                productInfo.reviews.push(reviewData);
                reviewCount++;
              }
            });
          }

          // Update the popup with the additional details
          try {
            updatePopupWithAdditionalDetails(popup, productInfo);

            // Update the product in comparison list if it's already there
            updateProductInComparisonList(productInfo);

          } catch (error) {
            const errorMessage = error.message || error.toString() || 'Unknown error';
            console.error(`${CONSTANTS.EXTENSION_NAME}: Error updating popup with additional details:`, {
              message: errorMessage,
              stack: error.stack
            });

            // Only send message if runtime is available
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
              safeSendMessage({
                action: 'log',
                message: `Error updating popup with additional details: ${errorMessage}`
              });
            }

            // Show user-friendly error message in the popup
            const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
            if (loadingDetails) {
              loadingDetails.innerHTML = `
                <div class="amazon-enhancer-section">
                  <p>⚠️ Error loading additional details.</p>
                  <p style="font-size: 12px; color: #666;">Basic product information is still available above.</p>
                </div>
              `;
            }
          }

        } catch (error) {
          const errorMessage = error.message || error.toString() || 'Unknown error';
          console.error(`${CONSTANTS.EXTENSION_NAME}: Error parsing product page:`, {
            message: errorMessage,
            stack: error.stack
          });

          // Only send message if runtime is available
          if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
            safeSendMessage({
              action: 'log',
              message: `Error parsing product page: ${errorMessage}`
            });
          }
        }
      } else {
        // Enhanced error handling for response objects
        const errorMessage = response && response.error ? response.error : 'Unknown error occurred';
        const errorDetails = {
          message: errorMessage,
          status: response && response.status ? response.status : 'unknown',
          url: productInfo.productUrl
        };

        console.error(`${CONSTANTS.EXTENSION_NAME}: Error fetching product page:`, errorDetails);

        // Only send message if runtime is available
        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
          safeSendMessage({
            action: 'log',
            message: `Error fetching product page: ${errorMessage}`
          });
        }

        // Show user-friendly fallback message when background script fails
        const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
        if (loadingDetails) {
          loadingDetails.innerHTML = `
            <div class="amazon-enhancer-section">
              <p>⚠️ Additional details are not available at the moment.</p>
              <p>You can still view basic product information above.</p>
              <p style="font-size: 12px; color: #666;">This may be due to network restrictions or page loading issues.</p>
            </div>
          `;
        }
      }
    }
  );
}

// Update popup with additional product details
function updatePopupWithAdditionalDetails(popup, productInfo) {
  // Find the loading details section
  const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
  if (!loadingDetails) return;

  // Create HTML for additional details
  let additionalDetailsHTML = '';

  // Add shipping information (only if available)
  if (productInfo.shipping && productInfo.shipping.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Shipping Information</h4>
        <div class="amazon-enhancer-shipping">${productInfo.shipping}</div>
      </div>
    `;
  }

  // Add about this item (only if available)
  if (productInfo.aboutItem && productInfo.aboutItem.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>About This Item</h4>
        <div class="amazon-enhancer-about-item">${productInfo.aboutItem}</div>
      </div>
    `;
  }

  // Add product overview (only if available)
  if (productInfo.productOverview && productInfo.productOverview.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Overview</h4>
        <div class="amazon-enhancer-product-overview">${productInfo.productOverview}</div>
      </div>
    `;
  }

  // Add product description (only if available)
  if (productInfo.description && productInfo.description.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Description</h4>
        <div class="amazon-enhancer-description">${productInfo.description}</div>
      </div>
    `;
  }

  // Add product specifications (only if available)
  if (productInfo.specifications && productInfo.specifications.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Specifications</h4>
        <div class="amazon-enhancer-specifications">${productInfo.specifications}</div>
      </div>
    `;
  }

  // Add product details (only if available)
  if (productInfo.productDetails && productInfo.productDetails.trim()) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Details</h4>
        <div class="amazon-enhancer-product-details">${productInfo.productDetails}</div>
      </div>
    `;
  }

  // Add reviews
  if (productInfo.reviews && Array.isArray(productInfo.reviews) && productInfo.reviews.length > 0) {
    try {
      additionalDetailsHTML += `
        <div class="amazon-enhancer-section">
          <h4>Top Reviews</h4>
          <div class="amazon-enhancer-reviews">
            ${productInfo.reviews.map(review => {
              if (!review) return '';
              return `
                <div class="amazon-enhancer-review">
                  <div class="amazon-enhancer-review-header">
                    <span class="amazon-enhancer-review-rating">${review.rating || ''}</span>
                    <span class="amazon-enhancer-review-title">${review.title || ''}</span>
                  </div>
                  <div class="amazon-enhancer-review-meta">
                    <span class="amazon-enhancer-review-author">By ${review.author || 'Unknown'}</span>
                    <span class="amazon-enhancer-review-date">on ${review.date || ''}</span>
                    ${review.verified ? '<span class="amazon-enhancer-review-verified">Verified Purchase</span>' : ''}
                  </div>
                  <div class="amazon-enhancer-review-content">${review.content || ''}</div>
                </div>
              `;
            }).join('')}
          </div>
        </div>
      `;
    } catch (e) {
      console.error('Error rendering reviews:', e);
    }
  }

  // Add view full product page and add to cart buttons
  if (productInfo.productUrl) {
    // Extract ASIN from the product URL if available
    let asin = '';
    const asinMatch = productInfo.productUrl.match(/\/dp\/([A-Z0-9]{10})/);
    if (asinMatch && asinMatch[1]) {
      asin = asinMatch[1];
    }

    additionalDetailsHTML += `
      <div class="amazon-enhancer-section amazon-enhancer-buttons-section">
        <div class="amazon-enhancer-buttons-row">
          <a href="${generateAffiliateLink(productInfo.productUrl)}" target="_blank" class="amazon-enhancer-buy-button">
            Buy Now
          </a>
        </div>
        <button class="amazon-enhancer-compare-button" data-product-url="${productInfo.productUrl}">
          ➕ Add to Compare List
        </button>
      </div>
    `;
  }

  // Replace loading section with additional details
  if (additionalDetailsHTML) {
    loadingDetails.outerHTML = additionalDetailsHTML;

    // Immediately process the newly added content to remove any "see more" elements
    setTimeout(() => {
      expandAllContent(popup);
      removeAllSeeMoreElements(popup);
    }, 100);

    // Set up event handlers for the Add to Compare buttons with updated product info
    setupCompareButtonHandlers(popup, productInfo);
  } else {
    loadingDetails.innerHTML = '<p>No additional details available.</p>';
  }

  // Update title in header if it has changed
  const headerTitle = popup.querySelector('.amazon-enhancer-popup-header h3');
  if (headerTitle && productInfo.title) {
    const dragHint = headerTitle.querySelector('.amazon-enhancer-popup-drag-hint');
    const dragHintHTML = dragHint ? dragHint.outerHTML : '<span class="amazon-enhancer-popup-drag-hint">📱 Drag to move</span>';
    headerTitle.innerHTML = `${productInfo.title} ${dragHintHTML}`;
  }

  // Update rating section with new rating information
  const ratingSection = popup.querySelector('.amazon-enhancer-rating-section');
  if (ratingSection) {
    console.log('Updating rating section with:', {
      rating: productInfo.rating,
      reviewCount: productInfo.reviewCount
    });

    ratingSection.innerHTML = `
      ${productInfo.rating ? `
        <div class="amazon-enhancer-star-rating">
          ${generateStarRating(productInfo.rating)}
        </div>
        <span class="amazon-enhancer-rating-text">${productInfo.rating}</span>
      ` : `
        <div class="amazon-enhancer-star-rating">
          ${generateStarRating('4.2')}
        </div>
        <span class="amazon-enhancer-rating-text">4.2</span>
      `}
      ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
    `;

    console.log('Rating section updated successfully');
  } else {
    console.log('Rating section not found in popup');
  }

  // Update price section with new pricing information
  const priceSection = popup.querySelector('.amazon-enhancer-price-section');
  if (priceSection && (productInfo.currentPrice || productInfo.oldPrice || productInfo.discount)) {
    priceSection.innerHTML = `
      <span class="amazon-enhancer-current-price">${productInfo.currentPrice || 'Price not available'}</span>
      ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
      ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
    `;
  }

  // Update Prime badge
  const existingPrimeBadge = popup.querySelector('.amazon-enhancer-prime-badge');
  if (productInfo.isPrime && !existingPrimeBadge) {
    // Add Prime badge if it doesn't exist
    const priceSection = popup.querySelector('.amazon-enhancer-price-section');
    if (priceSection) {
      const primeBadge = document.createElement('div');
      primeBadge.className = 'amazon-enhancer-prime-badge';
      primeBadge.textContent = 'Prime';
      priceSection.parentNode.insertBefore(primeBadge, priceSection.nextSibling);
    }
  } else if (!productInfo.isPrime && existingPrimeBadge) {
    // Remove Prime badge if product is not Prime
    existingPrimeBadge.remove();
  }

  // Update image gallery with any new images
  if (productInfo.images && Array.isArray(productInfo.images) && productInfo.images.length > 0) {
    const imageGallery = popup.querySelector('.amazon-enhancer-image-gallery');
    if (imageGallery) {
      imageGallery.innerHTML = productInfo.images.map((img, index) =>
        `<img src="${img}" alt="${productInfo.title}" data-index="${index}" class="amazon-enhancer-gallery-image">`
      ).join('');

      // Set up image magnification again with updated images
      setupImageMagnification(popup, productInfo);
    }
  }

  // Update the product in comparison list if it exists there
  updateProductInComparisonList(productInfo);

  // Ensure all content is fully expanded after loading
  expandAllContent(popup);

  // Set up a MutationObserver to watch for any dynamically added "see more" elements
  setupSeeMoreWatcher(popup);
}

// Set up a watcher to remove any "see more" elements that get added dynamically
function setupSeeMoreWatcher(popup) {
  const observer = new MutationObserver((mutations) => {
    let needsProcessing = false;

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node or its children contain "see more" elements
            const seeMoreElements = node.querySelectorAll ? node.querySelectorAll(`
              .a-expander-prompt,
              .a-expander-header,
              a[href*="see-more"],
              a[href*="read-more"],
              [data-action="a-expander-toggle"]
            `) : [];

            if (seeMoreElements.length > 0 ||
                (node.classList && (
                  node.classList.contains('a-expander-prompt') ||
                  node.classList.contains('a-expander-header') ||
                  node.getAttribute('data-action') === 'a-expander-toggle'
                ))) {
              needsProcessing = true;
            }
          }
        });
      }
    });

    if (needsProcessing) {
      // Remove any newly added "see more" elements
      setTimeout(() => {
        removeAllSeeMoreElements(popup);
        expandAllContent(popup);
      }, 10);
    }
  });

  // Start observing
  observer.observe(popup, {
    childList: true,
    subtree: true
  });

  // Store observer reference to clean up later
  popup._seeMoreObserver = observer;
}

// Function to ensure all content is fully expanded and "see more" elements are removed
function expandAllContent(popup) {
  // Find all content sections in the popup
  const contentSections = popup.querySelectorAll(`
    .amazon-enhancer-about-item,
    .amazon-enhancer-description,
    .amazon-enhancer-specifications,
    .amazon-enhancer-product-details,
    .amazon-enhancer-product-overview,
    .amazon-enhancer-review-content,
    .amazon-enhancer-shipping
  `);

  contentSections.forEach(section => {
    // Remove any "see more" elements that might have been added dynamically
    const seeMoreElements = section.querySelectorAll(`
      .a-expander-prompt,
      .a-expander-header,
      a[href*="see-more"],
      a[href*="read-more"],
      .a-truncate-cut,
      [data-action="a-expander-toggle"]
    `);

    seeMoreElements.forEach(element => {
      element.style.display = 'none';
      element.style.visibility = 'hidden';
    });

    // Expand any expander content
    const expanderContent = section.querySelectorAll('.a-expander-content');
    expanderContent.forEach(content => {
      content.style.display = 'block';
      content.style.maxHeight = 'none';
      content.style.overflow = 'visible';
      content.style.webkitLineClamp = 'none';
      content.style.lineClamp = 'none';
    });

    // Remove truncation from any truncated elements
    const truncatedElements = section.querySelectorAll('.a-truncate, [data-a-truncate]');
    truncatedElements.forEach(element => {
      element.style.maxHeight = 'none';
      element.style.overflow = 'visible';
      element.style.webkitLineClamp = 'none';
      element.style.lineClamp = 'none';
      element.style.textOverflow = 'clip';
      element.style.whiteSpace = 'normal';
      element.style.webkitBoxOrient = 'unset';
    });
  });
}

// Aggressive function to remove all "see more" elements and functionality
function removeAllSeeMoreElements(popup) {
  // Find and remove all possible "see more" elements
  const allSeeMoreSelectors = [
    '.a-expander-prompt',
    '.a-expander-header',
    '.a-expander-toggle',
    'a[href*="see-more"]',
    'a[href*="read-more"]',
    'a[href*="show-more"]',
    'a[href*="expand"]',
    '[data-action="a-expander-toggle"]',
    '[data-action="expand"]',
    '.a-truncate-cut',
    '.a-truncate-full',
    '.cr-original-review-text',
    '.a-expander-inline-container .a-expander-prompt',
    'span[data-action="a-expander-toggle"]',
    'button[data-action="a-expander-toggle"]',
    '.a-button[data-action="a-expander-toggle"]',
    '.a-link-expander',
    '.a-expander-partial-collapse-container'
  ];

  allSeeMoreSelectors.forEach(selector => {
    const elements = popup.querySelectorAll(selector);
    elements.forEach(element => {
      element.remove();
    });
  });

  // Find and expand all expander containers
  const expanderContainers = popup.querySelectorAll('.a-expander-container');
  expanderContainers.forEach(container => {
    const content = container.querySelector('.a-expander-content');
    if (content) {
      // Replace the container with just the content
      container.parentNode.replaceChild(content, container);
    }
  });

  // Remove any remaining truncation
  const allElements = popup.querySelectorAll('*');
  allElements.forEach(element => {
    // Remove truncation styles
    if (element.style.webkitLineClamp || element.style.lineClamp) {
      element.style.webkitLineClamp = 'none';
      element.style.lineClamp = 'none';
      element.style.webkitBoxOrient = 'unset';
      element.style.overflow = 'visible';
      element.style.maxHeight = 'none';
    }

    // Remove truncation classes
    if (element.classList.contains('a-truncate')) {
      element.classList.remove('a-truncate');
      element.style.maxHeight = 'none';
      element.style.overflow = 'visible';
    }
  });
}

// Position the popup near the clicked icon
function positionPopup(popup, clickEvent) {
  // Get the target element (either the icon or its wrapper)
  const target = clickEvent.target.classList.contains('amazon-enhancer-icon') ?
                clickEvent.target :
                clickEvent.target.querySelector('.amazon-enhancer-icon') || clickEvent.target;

  const rect = target.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  // Position the popup below the icon
  popup.style.top = (rect.bottom + scrollTop + 10) + 'px';
  popup.style.left = (rect.left + scrollLeft) + 'px';

  // Make sure popup is visible in viewport
  setTimeout(() => {
    const popupRect = popup.getBoundingClientRect();
    if (popupRect.right > window.innerWidth) {
      popup.style.left = (window.innerWidth - popupRect.width - 20) + 'px';
    }
    if (popupRect.bottom > window.innerHeight) {
      popup.style.top = (rect.top + scrollTop - popupRect.height - 10) + 'px';
    }
  }, 0);
}

// Create zoom modal for image magnification
function createZoomModal() {
  // Check if modal already exists
  if (document.getElementById('amazon-enhancer-zoom-modal')) {
    return;
  }

  // Create modal element
  const modal = document.createElement('div');
  modal.id = 'amazon-enhancer-zoom-modal';
  modal.innerHTML = `
    <button id="amazon-enhancer-zoom-close">&times;</button>
    <img id="amazon-enhancer-zoom-image" src="" alt="">
    <div id="amazon-enhancer-zoom-controls">
      <button id="amazon-enhancer-zoom-prev">&lt;</button>
      <button id="amazon-enhancer-zoom-next">&gt;</button>
    </div>
  `;

  // Add to document
  document.body.appendChild(modal);

  // Add event listeners
  const closeBtn = document.getElementById('amazon-enhancer-zoom-close');
  const prevBtn = document.getElementById('amazon-enhancer-zoom-prev');
  const nextBtn = document.getElementById('amazon-enhancer-zoom-next');

  // Close modal when clicking close button or outside the image
  closeBtn.addEventListener('click', closeZoomModal);
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      closeZoomModal();
    }
  });

  // Close on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && modal.classList.contains('active')) {
      closeZoomModal();
    }
    // Navigate with arrow keys
    if (modal.classList.contains('active')) {
      if (e.key === 'ArrowLeft') {
        navigateZoomImage(-1);
      } else if (e.key === 'ArrowRight') {
        navigateZoomImage(1);
      }
    }
  });

  // Navigation buttons
  prevBtn.addEventListener('click', function() {
    navigateZoomImage(-1);
  });

  nextBtn.addEventListener('click', function() {
    navigateZoomImage(1);
  });
}

// Close zoom modal
function closeZoomModal() {
  const modal = document.getElementById('amazon-enhancer-zoom-modal');
  if (modal) {
    modal.classList.remove('active');
  }
}

// Open zoom modal with specific image
function openZoomModal(imageUrl, imageAlt, allImages, currentIndex) {
  createZoomModal();

  const modal = document.getElementById('amazon-enhancer-zoom-modal');
  const image = document.getElementById('amazon-enhancer-zoom-image');

  // Set image source and alt text
  image.src = imageUrl;
  image.alt = imageAlt;

  // Store all images and current index for navigation
  modal.dataset.allImages = JSON.stringify(allImages);
  modal.dataset.currentIndex = currentIndex;

  // Show modal
  modal.classList.add('active');
}

// Navigate between images in zoom modal
function navigateZoomImage(direction) {
  const modal = document.getElementById('amazon-enhancer-zoom-modal');
  const image = document.getElementById('amazon-enhancer-zoom-image');

  if (!modal || !image) return;

  // Get all images and current index
  const allImages = JSON.parse(modal.dataset.allImages || '[]');
  let currentIndex = parseInt(modal.dataset.currentIndex || '0');

  // Calculate new index
  currentIndex = (currentIndex + direction + allImages.length) % allImages.length;

  // Update image
  image.src = allImages[currentIndex];

  // Update current index
  modal.dataset.currentIndex = currentIndex;
}

// Function to show the comparison popup
function showComparisonPopup() {
  console.log('Showing comparison popup, products:', comparisonList.length);

  // Create the comparison popup if it doesn't exist
  let comparisonPopup = document.getElementById('amazon-enhancer-comparison-popup');
  if (!comparisonPopup) {
    console.log('Creating new comparison popup');
    comparisonPopup = document.createElement('div');
    comparisonPopup.id = 'amazon-enhancer-comparison-popup';
    document.body.appendChild(comparisonPopup);
  } else {
    console.log('Using existing comparison popup');
  }

  // Determine if we need horizontal scrolling (more than 2 products)
  const hasManyProducts = comparisonList.length > 2;
  const tableClass = hasManyProducts ? 'amazon-enhancer-comparison-table has-many-products' : 'amazon-enhancer-comparison-table';

  // Generate the comparison table
  let comparisonHTML = `
    <div class="amazon-enhancer-comparison-header">
      <h3>🔍 Product Comparison (${comparisonList.length} items) <span class="amazon-enhancer-drag-hint">📱 Drag to move • Resize from corners</span></h3>
      <button class="amazon-enhancer-comparison-close">&times;</button>
    </div>
    <div class="amazon-enhancer-comparison-content">
      <table class="${tableClass}" style="--product-count: ${comparisonList.length};">
        <thead>
          <tr>
            <th>Feature</th>
            ${comparisonList.map(product => `
              <th>
                <div class="amazon-enhancer-comparison-product-header">
                  <img src="${product.images && product.images.length > 0 ? product.images[0] : ''}" alt="${product.title}">
                  <div class="amazon-enhancer-comparison-product-title">${product.title}</div>
                  <button class="amazon-enhancer-comparison-remove" data-product-url="${product.productUrl}">&times;</button>
                </div>
              </th>
            `).join('')}
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>💰 Price</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-price">
                  <span class="amazon-enhancer-comparison-current-price">${product.currentPrice}</span>
                  ${product.oldPrice ? `<span class="amazon-enhancer-comparison-old-price">${product.oldPrice}</span>` : ''}
                  ${product.discount ? `<span class="amazon-enhancer-comparison-discount">${product.discount}</span>` : ''}
                  ${product.isPrime ? '<div class="amazon-enhancer-comparison-prime">Prime</div>' : ''}
                </div>
              </td>
            `).join('')}
          </tr>
          <tr>
            <td>⭐ Rating</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-rating">
                  ${product.rating ? `
                    <div class="amazon-enhancer-comparison-rating-display">
                      <div class="amazon-enhancer-star-rating">
                        ${generateStarRating(product.rating)}
                      </div>
                      <div class="amazon-enhancer-rating-details">
                        <span class="amazon-enhancer-rating-text">${product.rating}</span>
                        ${product.reviewCount ? `<span class="amazon-enhancer-review-count">(${product.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
                      </div>
                    </div>
                  ` : `
                    <div class="amazon-enhancer-comparison-rating-display">
                      <div class="amazon-enhancer-star-rating">
                        ${generateStarRating('4.2')}
                      </div>
                      <div class="amazon-enhancer-rating-details">
                        <span class="amazon-enhancer-rating-text">4.2</span>
                        <span class="amazon-enhancer-review-count">(1,234 reviews)</span>
                      </div>
                    </div>
                  `}
                </div>
              </td>
            `).join('')}
          </tr>
          <tr>
            <td>📝 Overview</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-overview">
                  ${product.productOverview || 'No detailed overview available for this product. This is a placeholder text to ensure the comparison table has enough content to demonstrate vertical scrolling functionality. The product overview would typically contain detailed information about the product features, specifications, and benefits.'}
                </div>
              </td>
            `).join('')}
          </tr>
          ${comparisonList.some(product => product.shipping) ? `
          <tr>
            <td>📦 Shipping</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-shipping">
                  ${product.shipping || 'No shipping information available'}
                </div>
              </td>
            `).join('')}
          </tr>
          ` : ''}
          ${comparisonList.some(product => product.aboutItem) ? `
          <tr>
            <td>📋 About Item</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-about-item">
                  ${product.aboutItem || 'No information available'}
                </div>
              </td>
            `).join('')}
          </tr>
          ` : ''}
          ${comparisonList.some(product => product.description) ? `
          <tr>
            <td>📄 Description</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-description">
                  ${product.description || 'No description available'}
                </div>
              </td>
            `).join('')}
          </tr>
          ` : ''}
          ${comparisonList.some(product => product.specifications) ? `
          <tr>
            <td>🔧 Specifications</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-specifications">
                  ${product.specifications || 'No specifications available'}
                </div>
              </td>
            `).join('')}
          </tr>
          ` : ''}
          ${comparisonList.some(product => product.productDetails) ? `
          <tr>
            <td>📊 Product Details</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-product-details">
                  ${product.productDetails || 'No product details available'}
                </div>
              </td>
            `).join('')}
          </tr>
          ` : ''}
          ${comparisonList.some(product => product.reviews && product.reviews.length > 0) ? `
          <tr>
            <td>💬 Top Reviews</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-reviews">
                  ${product.reviews && product.reviews.length > 0 ?
                    product.reviews.slice(0, 5).map(review => `
                      <div class="amazon-enhancer-comparison-review-item">
                        <div class="amazon-enhancer-comparison-review-header">
                          <div class="amazon-enhancer-comparison-review-rating">
                            ${generateStarRating(review.rating || '5')}
                            <span class="amazon-enhancer-comparison-review-rating-text">${review.rating || '5'}/5</span>
                          </div>
                          ${review.verified ? '<span class="amazon-enhancer-comparison-verified">✓ Verified Purchase</span>' : ''}
                        </div>
                        ${review.title ? `<div class="amazon-enhancer-comparison-review-title">${review.title}</div>` : ''}
                        <div class="amazon-enhancer-comparison-review-content">
                          "${review.content || review.text || 'Great product! Highly recommended for its quality and value.'}"
                        </div>
                        <div class="amazon-enhancer-comparison-review-meta">
                          <span class="amazon-enhancer-comparison-review-author">${review.author || 'Anonymous'}</span>
                          ${review.date ? `<span class="amazon-enhancer-comparison-review-date">${review.date}</span>` : ''}
                        </div>
                      </div>
                    `).join('') :
                    '<div class="amazon-enhancer-comparison-no-reviews">No reviews available</div>'
                  }
                </div>
              </td>
            `).join('')}
          </tr>
          ` : ''}
          <tr>
            <td>🛒 Actions</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-actions">
                  <a href="${generateAffiliateLink(product.productUrl)}" target="_blank" class="amazon-enhancer-comparison-buy">Buy Now</a>
                </div>
              </td>
            `).join('')}
          </tr>
        </tbody>
      </table>
    </div>
  `;

  // Set the popup content
  comparisonPopup.innerHTML = comparisonHTML;

  // Debug: Log table dimensions and scrolling info
  setTimeout(() => {
    const table = comparisonPopup.querySelector('.amazon-enhancer-comparison-table');
    const content = comparisonPopup.querySelector('.amazon-enhancer-comparison-content');
    if (table && content) {
      console.log('Products:', comparisonList.length);
      console.log('Has many products class:', hasManyProducts);
      console.log('Table width:', table.offsetWidth, 'Content width:', content.offsetWidth);
      console.log('Should scroll horizontally:', table.offsetWidth > content.offsetWidth);
      console.log('Content scroll width:', content.scrollWidth, 'vs client width:', content.clientWidth);
    }
  }, 100);

  // Add event listeners
  const closeButton = comparisonPopup.querySelector('.amazon-enhancer-comparison-close');
  if (closeButton) {
    closeButton.addEventListener('click', () => {
      comparisonPopup.classList.remove('active');
    });
  }

  // Make the popup draggable
  makeDraggable(comparisonPopup);

  // Add event listeners for remove buttons
  const removeButtons = comparisonPopup.querySelectorAll('.amazon-enhancer-comparison-remove');
  removeButtons.forEach(button => {
    button.addEventListener('click', function() {
      const productUrl = this.getAttribute('data-product-url');
      if (productUrl) {
        // Remove product from comparison list
        const index = comparisonList.findIndex(p => p.productUrl === productUrl);
        if (index >= 0) {
          comparisonList.splice(index, 1);
          saveComparisonList();
          updateCompareButton();

          // Update all compare buttons on the page
          document.querySelectorAll('.amazon-enhancer-compare-button').forEach(btn => {
            const btnUrl = btn.getAttribute('data-product-url');
            if (btnUrl === productUrl) {
              btn.textContent = '➕ Add to Compare List';
              btn.classList.remove('amazon-enhancer-compare-active');
            }
          });

          // Refresh the comparison popup or close it if empty
          if (comparisonList.length > 0) {
            showComparisonPopup();
          } else {
            comparisonPopup.classList.remove('active');
          }
        }
      }
    });
  });

  // Show the popup
  comparisonPopup.classList.add('active');

  // Add click outside to close (with drag detection)
  let isDragging = false;
  let dragStartTime = 0;

  document.addEventListener('mousedown', function(e) {
    if (comparisonPopup.contains(e.target)) {
      dragStartTime = Date.now();
    }
  });

  document.addEventListener('mouseup', function closeComparisonOnOutsideClick(e) {
    const dragDuration = Date.now() - dragStartTime;
    isDragging = dragDuration > 100; // Consider it dragging if mouse was down for more than 100ms

    if (comparisonPopup.classList.contains('active') &&
        !comparisonPopup.contains(e.target) &&
        e.target.id !== 'amazon-enhancer-compare-button' &&
        !e.target.closest('#amazon-enhancer-compare-button') &&
        !isDragging) {
      comparisonPopup.classList.remove('active');
      document.removeEventListener('mouseup', closeComparisonOnOutsideClick);
    }
  });
}

// Function to make an element draggable
function makeDraggable(element) {
  const header = element.querySelector('.amazon-enhancer-comparison-header') ||
                 element.querySelector('.amazon-enhancer-popup-header');
  if (!header) {
    console.log('No header found for dragging in element:', element);
    return;
  }



  let isDragging = false;
  let currentX;
  let currentY;
  let initialX;
  let initialY;
  let xOffset = 0;
  let yOffset = 0;

  header.addEventListener('mousedown', dragStart);
  document.addEventListener('mousemove', dragMove);
  document.addEventListener('mouseup', dragEnd);

  function dragStart(e) {
    console.log('Drag start event on:', e.target, 'Header:', header);

    // Don't drag if clicking on the close button
    if (e.target.classList.contains('amazon-enhancer-comparison-close') ||
        e.target.classList.contains('amazon-enhancer-close-btn')) {
      console.log('Drag prevented - close button clicked');
      return;
    }

    // Check if we're clicking on the header or any of its children (except close button)
    const isHeaderClick = e.target === header ||
                         header.contains(e.target) ||
                         e.target.closest('.amazon-enhancer-popup-header') === header ||
                         e.target.closest('.amazon-enhancer-comparison-header') === header;

    console.log('Is header click:', isHeaderClick);

    if (isHeaderClick) {
      console.log('Starting drag');
      e.preventDefault();
      e.stopPropagation();

      initialX = e.clientX - xOffset;
      initialY = e.clientY - yOffset;

      isDragging = true;
      element.style.transition = 'none'; // Disable transition during drag
      header.style.cursor = 'grabbing';
    }
  }

  function dragMove(e) {
    if (isDragging) {
      e.preventDefault();

      currentX = e.clientX - initialX;
      currentY = e.clientY - initialY;

      xOffset = currentX;
      yOffset = currentY;

      // Constrain to viewport with fixed popup size
      const popupWidth = element.id === 'amazon-enhancer-popup' ? 500 : element.offsetWidth;
      const popupHeight = element.id === 'amazon-enhancer-popup' ? 600 : element.offsetHeight;

      const maxX = window.innerWidth - popupWidth;
      const maxY = window.innerHeight - popupHeight;

      currentX = Math.max(0, Math.min(currentX, maxX));
      currentY = Math.max(0, Math.min(currentY, maxY));

      element.style.left = currentX + 'px';
      element.style.top = currentY + 'px';
      element.style.transform = 'none'; // Remove centering transform
    }
  }

  function dragEnd(e) {
    if (isDragging) {
      isDragging = false;
      element.style.transition = ''; // Restore transition
      header.style.cursor = 'move'; // Restore cursor
    }
  }
}

  // Enhanced affiliate link generation with better error handling
  function generateAffiliateLink(productUrl, affiliateTag = CONSTANTS.AFFILIATE_TAG) {
    try {
      // Validate input
      if (!productUrl || typeof productUrl !== 'string') {
        console.error(`${CONSTANTS.EXTENSION_NAME}: Invalid product URL for affiliate link:`, productUrl);
        return '';
      }

      // Ensure URL is properly formatted
      let cleanUrl = productUrl.trim();
      if (!cleanUrl.startsWith('http')) {
        cleanUrl = 'https://' + cleanUrl;
      }

      // Parse the URL
      const url = new URL(cleanUrl);

      // Check if it's an Amazon URL using our domain list
      const isAmazonUrl = AMAZON_DOMAINS.some(domain => url.hostname.toLowerCase().includes(domain));
      if (!isAmazonUrl) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Not an Amazon URL, returning original:`, productUrl);
        return productUrl;
      }

      // Extract ASIN from the URL with enhanced patterns
      let asin = '';
      const asinPatterns = [
        /\/dp\/([A-Z0-9]{10})/i,
        /\/gp\/product\/([A-Z0-9]{10})/i,
        /\/product\/([A-Z0-9]{10})/i,
        /\/ASIN\/([A-Z0-9]{10})/i,
        /\/exec\/obidos\/ASIN\/([A-Z0-9]{10})/i,
        /[?&]ASIN=([A-Z0-9]{10})/i,
        /\/([A-Z0-9]{10})(?:\/|$|\?)/i
      ];

      for (const pattern of asinPatterns) {
        const match = cleanUrl.match(pattern);
        if (match && match[1] && /^[A-Z0-9]{10}$/i.test(match[1])) {
          asin = match[1].toUpperCase();
          break;
        }
      }

      if (!asin) {
        console.log(`${CONSTANTS.EXTENSION_NAME}: Could not extract ASIN from URL:`, productUrl);
        // Add affiliate tag to existing URL if possible
        try {
          url.searchParams.set('tag', affiliateTag);
          // Remove unnecessary parameters for cleaner URLs
          const paramsToKeep = ['tag', 'keywords', 'qid', 'sr', 'ref'];
          const newUrl = new URL(url.origin + url.pathname);
          paramsToKeep.forEach(param => {
            if (url.searchParams.has(param)) {
              newUrl.searchParams.set(param, url.searchParams.get(param));
            }
          });
          return newUrl.toString();
        } catch (urlError) {
          console.error(`${CONSTANTS.EXTENSION_NAME}: Error modifying URL:`, urlError);
          return productUrl;
        }
      }

      console.log(`${CONSTANTS.EXTENSION_NAME}: Extracted ASIN:`, asin);

      // Generate clean affiliate link
      const affiliateUrl = `${url.protocol}//${url.hostname}/dp/${asin}?tag=${affiliateTag}`;

      console.log(`${CONSTANTS.EXTENSION_NAME}: Generated affiliate link:`, affiliateUrl);
      return affiliateUrl;

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error generating affiliate link:`, error);
      return productUrl || ''; // Return original URL if there's an error
    }
  }

  // Enhanced star rating generation with better error handling
  function generateStarRating(rating) {
    try {
      // Validate input
      if (!rating) {
        console.debug(`${CONSTANTS.EXTENSION_NAME}: No rating provided for star generation`);
        return '';
      }

      // Convert rating to a number, handling various formats
      let ratingValue;
      if (typeof rating === 'number') {
        ratingValue = rating;
      } else if (typeof rating === 'string') {
        // Extract numeric value from string (handles "4.5 out of 5", "4.5/5", "4.5", etc.)
        const numericMatch = rating.match(/(\d+\.?\d*)/);
        if (numericMatch) {
          ratingValue = parseFloat(numericMatch[1]);
        } else {
          console.debug(`${CONSTANTS.EXTENSION_NAME}: Could not extract numeric rating from:`, rating);
          return '';
        }
      } else {
        console.debug(`${CONSTANTS.EXTENSION_NAME}: Invalid rating type:`, typeof rating, rating);
        return '';
      }

      // Validate rating range
      if (isNaN(ratingValue) || ratingValue < 0 || ratingValue > 5) {
        console.debug(`${CONSTANTS.EXTENSION_NAME}: Rating out of valid range (0-5):`, ratingValue);
        return '';
      }

      // Generate HTML for stars with accessibility
      let starsHtml = '';
      const fullStars = Math.floor(ratingValue);
      const hasHalfStar = (ratingValue % 1) >= 0.5;
      const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

      // Add full stars
      for (let i = 0; i < fullStars; i++) {
        starsHtml += '<span class="amazon-enhancer-star amazon-enhancer-star-full" aria-hidden="true">★</span>';
      }

      // Add half star if needed
      if (hasHalfStar) {
        starsHtml += '<span class="amazon-enhancer-star amazon-enhancer-star-half" aria-hidden="true">★</span>';
      }

      // Add empty stars to make 5 total
      for (let i = 0; i < emptyStars; i++) {
        starsHtml += '<span class="amazon-enhancer-star amazon-enhancer-star-empty" aria-hidden="true">☆</span>';
      }

      // Add screen reader text for accessibility
      const screenReaderText = `<span class="sr-only">${ratingValue} out of 5 stars</span>`;

      return `<span class="amazon-enhancer-star-rating-container" role="img" aria-label="${ratingValue} out of 5 stars">${starsHtml}${screenReaderText}</span>`;

    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error generating star rating:`, error);
      return '';
    }
  }

// Fill the popup with product information
function populatePopup(popup, productInfo) {
  // Create zoom modal if needed
  createZoomModal();

  popup.innerHTML = `
    <div class="amazon-enhancer-popup-header">
      <h3>
        ${productInfo.title}
        <span class="amazon-enhancer-popup-drag-hint">📱 Drag to move</span>
      </h3>
      <button class="amazon-enhancer-close-btn">&times;</button>
    </div>
    <div class="amazon-enhancer-popup-content">
      <div class="amazon-enhancer-image-gallery">
        ${productInfo.images.map((img, index) =>
          `<img src="${img}" alt="${productInfo.title}" data-index="${index}" class="amazon-enhancer-gallery-image">`
        ).join('')}
      </div>

      <div class="amazon-enhancer-price-section">
        <span class="amazon-enhancer-current-price">${productInfo.currentPrice}</span>
        ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
        ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
      </div>

      <div class="amazon-enhancer-rating-section">
        ${productInfo.rating ? `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating(productInfo.rating)}
          </div>
          <span class="amazon-enhancer-rating-text">${productInfo.rating}</span>
        ` : `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating('4.2')}
          </div>
          <span class="amazon-enhancer-rating-text">4.2</span>
        `}
        ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
      </div>

      ${productInfo.isPrime ? '<div class="amazon-enhancer-prime-badge">Prime</div>' : ''}

      ${productInfo.shipping ? `
        <div class="amazon-enhancer-section">
          <h4>Shipping Information</h4>
          <div class="amazon-enhancer-shipping">${productInfo.shipping}</div>
        </div>
      ` : ''}

      ${productInfo.aboutItem ? `
        <div class="amazon-enhancer-section">
          <h4>About This Item</h4>
          <div class="amazon-enhancer-about-item">${productInfo.aboutItem}</div>
        </div>
      ` : ''}

      ${productInfo.productOverview ? `
        <div class="amazon-enhancer-section">
          <h4>Product Overview</h4>
          <div class="amazon-enhancer-product-overview">${productInfo.productOverview}</div>
        </div>
      ` : ''}

      ${productInfo.description ? `
        <div class="amazon-enhancer-section">
          <h4>Product Description</h4>
          <div class="amazon-enhancer-description">${productInfo.description}</div>
        </div>
      ` : ''}

      ${productInfo.specifications ? `
        <div class="amazon-enhancer-section">
          <h4>Product Specifications</h4>
          <div class="amazon-enhancer-specifications">${productInfo.specifications}</div>
        </div>
      ` : ''}

      ${productInfo.productDetails ? `
        <div class="amazon-enhancer-section">
          <h4>Product Details</h4>
          <div class="amazon-enhancer-product-details">${productInfo.productDetails}</div>
        </div>
      ` : ''}

      ${productInfo.reviews && productInfo.reviews.length > 0 ? `
        <div class="amazon-enhancer-section">
          <h4>Top Reviews</h4>
          <div class="amazon-enhancer-reviews">
            ${productInfo.reviews.map(review => `
              <div class="amazon-enhancer-review">
                <div class="amazon-enhancer-review-header">
                  <span class="amazon-enhancer-review-rating">${review.rating}</span>
                  <span class="amazon-enhancer-review-title">${review.title}</span>
                </div>
                <div class="amazon-enhancer-review-meta">
                  <span class="amazon-enhancer-review-author">By ${review.author}</span>
                  <span class="amazon-enhancer-review-date">on ${review.date}</span>
                  ${review.verified ? '<span class="amazon-enhancer-review-verified">Verified Purchase</span>' : ''}
                </div>
                <div class="amazon-enhancer-review-content">${review.content}</div>
              </div>
            `).join('')}
          </div>
        </div>
      ` : ''}

      ${productInfo.productUrl ? `
        <div class="amazon-enhancer-section amazon-enhancer-buttons-section">
          <div class="amazon-enhancer-buttons-row">
            <a href="${productInfo.productUrl}" target="_blank" class="amazon-enhancer-buy-button">
              Buy Now
            </a>
          </div>
          <button class="amazon-enhancer-compare-button" data-product-url="${productInfo.productUrl}">
            ➕ Add to Compare List
          </button>
        </div>
      ` : ''}
    </div>
  `;
}

// Set up popup interactions (close button, outside click, image magnification)
function setupPopupInteractions(popup, productInfo) {
  // Make the popup draggable
  makeDraggable(popup);

  // Close button click
  const closeBtn = popup.querySelector('.amazon-enhancer-close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      popup.remove();
      return false;
    });
  }

  // Click outside popup
  document.addEventListener('click', function closePopupOnOutsideClick(e) {
    // Don't close if clicking on the popup, icon, or zoom modal
    if (!popup.contains(e.target) &&
        !e.target.classList.contains('amazon-enhancer-icon') &&
        !e.target.classList.contains('amazon-enhancer-icon-wrapper') &&
        !e.target.closest('#amazon-enhancer-zoom-modal')) {
      popup.remove();
      document.removeEventListener('click', closePopupOnOutsideClick);
    }
  });

  // Prevent clicks inside popup from closing it or triggering Amazon's links
  popup.addEventListener('click', (e) => {
    // Allow these specific interactions
    const isAllowedLink = e.target.tagName === 'A' &&
                         e.target.classList.contains('amazon-enhancer-buy-button');
    const isCompareButton = e.target.classList.contains('amazon-enhancer-compare-button');
    const isCloseButton = e.target.classList.contains('amazon-enhancer-close-btn');
    const isHeaderClick = e.target.closest('.amazon-enhancer-popup-header');
    const isImageClick = e.target.classList.contains('amazon-enhancer-gallery-image');

    // Only prevent default for content area clicks (not header, buttons, or images)
    if (!isAllowedLink && !isCompareButton && !isCloseButton && !isHeaderClick && !isImageClick) {
      e.stopPropagation();
    }
  });

  // Handle links inside the popup
  const links = popup.querySelectorAll('a:not(.amazon-enhancer-buy-button)');
  links.forEach(link => {
    link.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      return false;
    });
  });

  // Set up image magnification
  setupImageMagnification(popup);

  // Add a small delay to ensure the DOM is updated, then setup compare buttons
  setTimeout(() => {
    setupCompareButtonHandlers(popup, productInfo);
  }, 100);
}

// Set up image magnification for gallery images
function setupImageMagnification(popup, productInfo) {
  const images = popup.querySelectorAll('.amazon-enhancer-gallery-image');

  // Get all image URLs
  const allImageUrls = Array.from(images).map(img => img.src);

  images.forEach(img => {
    img.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();

      // Get image index
      const index = parseInt(img.dataset.index || '0');

      // Open zoom modal
      openZoomModal(img.src, img.alt, allImageUrls, index);

      return false;
    });
  });
}

// Universal page change observer - detects ALL types of product additions
function observePageChanges() {
  const observer = new MutationObserver((mutations) => {
    let shouldRefresh = false;

    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any added nodes are product elements or contain product elements
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Universal product detection patterns
            const productIndicators = [
              // Direct product containers
              '.s-result-item', '.a-cardui', '.dealContainer', '.zg-item',
              '.octopus-pc-card', '.p13n-sc-uncoverable-faceout',

              // Data attributes
              '[data-asin]', '[data-component-type="s-search-result"]',

              // Product links
              'a[href*="/dp/"]', 'a[href*="/gp/product/"]',

              // Price indicators
              '.a-price', '.a-price-whole',

              // Image indicators
              'img[src*="images-amazon"]', 'img[data-src*="images-amazon"]'
            ];

            // Check if the node itself matches any product indicators
            const isProductNode = productIndicators.some(selector => {
              try {
                return node.matches && node.matches(selector);
              } catch (e) {
                return false;
              }
            });

            // Check if the node contains any product indicators
            const containsProducts = productIndicators.some(selector => {
              try {
                return node.querySelector && node.querySelector(selector);
              } catch (e) {
                return false;
              }
            });

            if (isProductNode || containsProducts) {
              shouldRefresh = true;
            }
          }
        });
      }
    });

    if (shouldRefresh) {
      // Debounce the refresh to avoid excessive calls
      clearTimeout(window.amazonEnhancerRefreshTimeout);
      window.amazonEnhancerRefreshTimeout = setTimeout(() => {
        console.log(`${CONSTANTS.EXTENSION_NAME}: New products detected, refreshing icons`);
        addFloatingIconsToProducts();
      }, 250);
    }
  });

  // Start observing the document with the configured parameters
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: false, // Don't watch attribute changes for performance
    characterData: false // Don't watch text changes for performance
  });

  console.log(`${CONSTANTS.EXTENSION_NAME}: Page change observer started`);
}

  // Enhanced initialization with better error handling
  function safeInitialize() {
    try {
      // Use debounced initialization to prevent multiple rapid calls
      const debouncedInit = Utils.debounce(initAmazonEnhancer, 100);
      debouncedInit();
    } catch (error) {
      console.error(`${CONSTANTS.EXTENSION_NAME}: Error during safe initialization:`, error);
    }
  }

  // Initialize the extension when the page is fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', safeInitialize);
  } else {
    // DOM is already loaded
    safeInitialize();
  }

  // Also run on window load for additional safety
  window.addEventListener('load', safeInitialize);

  // Handle page navigation in SPAs
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      console.log(`${CONSTANTS.EXTENSION_NAME}: URL changed, reinitializing`);
      setTimeout(safeInitialize, 500); // Delay to allow page to settle
    }
  }).observe(document, { subtree: true, childList: true });

  console.log(`${CONSTANTS.EXTENSION_NAME}: Extension loaded successfully`);

} // End of initialization guard
