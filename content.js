// Content script for Amazon Product Info Enhancer

// Main function to initialize the extension
function initAmazonEnhancer() {
  // Check if we're on an Amazon product listing page
  if (isProductListingPage()) {
    console.log('Amazon Product Info Enhancer: Product listing page detected');
    addFloatingIconsToProducts();
    
    // Add event listener for dynamic content loading (Amazon loads products dynamically)
    observePageChanges();
  }
}

// Check if the current page is a product listing page
function isProductListingPage() {
  // Amazon search results page
  if (window.location.href.includes('/s?') || 
      window.location.href.includes('/s/') ||
      window.location.href.includes('/gp/bestsellers') ||
      window.location.href.includes('/gp/new-releases')) {
    return true;
  }
  
  // Check for product grid elements that typically appear on listing pages
  const productGrids = document.querySelectorAll('.s-result-list, .s-search-results');
  return productGrids.length > 0;
}

// Add floating icons to all product images on the page
function addFloatingIconsToProducts() {
  // Find all product containers
  const productElements = document.querySelectorAll('.s-result-item');
  
  productElements.forEach((product, index) => {
    // Find the product image container
    const imageContainer = product.querySelector('.s-image-container, .a-section.aok-relative');
    if (!imageContainer) return;
    
    // Check if we already added an icon to this product
    if (imageContainer.querySelector('.amazon-enhancer-icon')) return;
    
    // Create floating icon
    const floatingIcon = document.createElement('div');
    floatingIcon.className = 'amazon-enhancer-icon';
    floatingIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>';
    
    // Add data attribute to identify the product
    floatingIcon.dataset.productIndex = index;
    
    // Add click event listener
    floatingIcon.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent triggering Amazon's click
      showProductInfoPopup(product, e);
    });
    
    // Append icon to image container
    imageContainer.appendChild(floatingIcon);
  });
}

// Show product info popup when icon is clicked
function showProductInfoPopup(productElement, clickEvent) {
  // Extract product information
  const productInfo = extractProductInfo(productElement);
  
  // Create popup element if it doesn't exist
  let popup = document.getElementById('amazon-enhancer-popup');
  if (!popup) {
    popup = document.createElement('div');
    popup.id = 'amazon-enhancer-popup';
    document.body.appendChild(popup);
  }
  
  // Position popup near the clicked icon
  positionPopup(popup, clickEvent);
  
  // Fill popup with product information
  populatePopup(popup, productInfo);
  
  // Add close button and outside click handler
  setupPopupInteractions(popup);
}

// Extract product information from the product element
function extractProductInfo(productElement) {
  // Initialize product info object
  const info = {
    title: '',
    images: [],
    currentPrice: '',
    oldPrice: '',
    discount: '',
    rating: '',
    reviewCount: '',
    isPrime: false,
    seller: '',
    aboutItem: '',
    description: '',
    shipping: ''
  };
  
  // Extract title
  const titleElement = productElement.querySelector('h2');
  if (titleElement) {
    info.title = titleElement.textContent.trim();
  }
  
  // Extract current price
  const priceElement = productElement.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    info.currentPrice = priceElement.textContent.trim();
  }
  
  // Extract old price if available
  const oldPriceElement = productElement.querySelector('.a-price.a-text-price .a-offscreen');
  if (oldPriceElement) {
    info.oldPrice = oldPriceElement.textContent.trim();
  }
  
  // Extract rating
  const ratingElement = productElement.querySelector('.a-icon-star-small');
  if (ratingElement) {
    info.rating = ratingElement.textContent.trim();
  }
  
  // Extract review count
  const reviewCountElement = productElement.querySelector('.a-size-base.s-underline-text');
  if (reviewCountElement) {
    info.reviewCount = reviewCountElement.textContent.trim();
  }
  
  // Check for Prime
  info.isPrime = !!productElement.querySelector('.s-prime');
  
  // Extract seller/brand if available
  const sellerElement = productElement.querySelector('.a-row.a-size-base.a-color-secondary .a-size-base');
  if (sellerElement) {
    info.seller = sellerElement.textContent.trim();
  }
  
  // Extract main image
  const imageElement = productElement.querySelector('.s-image');
  if (imageElement && imageElement.src) {
    info.images.push(imageElement.src);
    
    // Try to get higher resolution image
    const highResImage = imageElement.src.replace(/_AC_UL\d+_/, '_AC_UL1500_');
    if (highResImage !== imageElement.src) {
      info.images.push(highResImage);
    }
  }
  
  // For shipping and other details, we'll need to fetch the product page
  // This would be implemented in a more advanced version
  
  return info;
}

// Position the popup near the clicked icon
function positionPopup(popup, clickEvent) {
  const rect = clickEvent.target.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
  
  popup.style.top = (rect.bottom + scrollTop + 10) + 'px';
  popup.style.left = (rect.left + scrollLeft) + 'px';
  
  // Make sure popup is visible in viewport
  setTimeout(() => {
    const popupRect = popup.getBoundingClientRect();
    if (popupRect.right > window.innerWidth) {
      popup.style.left = (window.innerWidth - popupRect.width - 20) + 'px';
    }
    if (popupRect.bottom > window.innerHeight) {
      popup.style.top = (rect.top + scrollTop - popupRect.height - 10) + 'px';
    }
  }, 0);
}

// Fill the popup with product information
function populatePopup(popup, productInfo) {
  popup.innerHTML = `
    <div class="amazon-enhancer-popup-header">
      <h3>${productInfo.title}</h3>
      <button class="amazon-enhancer-close-btn">&times;</button>
    </div>
    <div class="amazon-enhancer-popup-content">
      <div class="amazon-enhancer-image-gallery">
        ${productInfo.images.map(img => `<img src="${img}" alt="${productInfo.title}">`).join('')}
      </div>
      <div class="amazon-enhancer-price-section">
        <span class="amazon-enhancer-current-price">${productInfo.currentPrice}</span>
        ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
      </div>
      <div class="amazon-enhancer-rating-section">
        ${productInfo.rating ? `<span class="amazon-enhancer-rating">${productInfo.rating}</span>` : ''}
        ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : ''}
      </div>
      ${productInfo.isPrime ? '<div class="amazon-enhancer-prime-badge">Prime</div>' : ''}
      ${productInfo.seller ? `<div class="amazon-enhancer-seller">Seller: ${productInfo.seller}</div>` : ''}
      ${productInfo.shipping ? `<div class="amazon-enhancer-shipping">${productInfo.shipping}</div>` : ''}
    </div>
  `;
}

// Set up popup interactions (close button, outside click)
function setupPopupInteractions(popup) {
  // Close button click
  const closeBtn = popup.querySelector('.amazon-enhancer-close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', () => {
      popup.remove();
    });
  }
  
  // Click outside popup
  document.addEventListener('click', function closePopupOnOutsideClick(e) {
    if (!popup.contains(e.target) && !e.target.classList.contains('amazon-enhancer-icon')) {
      popup.remove();
      document.removeEventListener('click', closePopupOnOutsideClick);
    }
  });
  
  // Prevent clicks inside popup from closing it
  popup.addEventListener('click', (e) => {
    e.stopPropagation();
  });
}

// Observe page changes for dynamically loaded content
function observePageChanges() {
  const observer = new MutationObserver((mutations) => {
    let shouldRefresh = false;
    
    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any added nodes are product elements or contain product elements
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.classList && (
                node.classList.contains('s-result-item') || 
                node.querySelector('.s-result-item')
            )) {
              shouldRefresh = true;
            }
          }
        });
      }
    });
    
    if (shouldRefresh) {
      addFloatingIconsToProducts();
    }
  });
  
  // Start observing the document with the configured parameters
  observer.observe(document.body, { childList: true, subtree: true });
}

// Initialize the extension when the page is fully loaded
window.addEventListener('load', initAmazonEnhancer);

// Also run on DOM content loaded to handle static content
document.addEventListener('DOMContentLoaded', initAmazonEnhancer);
