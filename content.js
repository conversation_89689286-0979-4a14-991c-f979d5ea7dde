// Content script for Amazon Product Info Enhancer

// Prevent multiple initializations
if (typeof window.amazonEnhancerInitialized === 'undefined') {
  window.amazonEnhancerInitialized = true;

  // Global storage for product comparison
  let comparisonList = [];

// Load comparison list from localStorage
function loadComparisonList() {
  console.log('Loading comparison list from localStorage');
  try {
    const savedList = localStorage.getItem('amazon-enhancer-comparison-list');
    if (savedList) {
      comparisonList = JSON.parse(savedList);
      console.log('Loaded comparison list:', comparisonList.length, 'products');
      updateCompareButton();
    } else {
      console.log('No saved comparison list found');
      comparisonList = [];
    }
  } catch (e) {
    console.error('Error loading comparison list:', e);
    comparisonList = [];
  }
}

// Save comparison list to localStorage
function saveComparisonList() {
  console.log('Saving comparison list to localStorage:', comparisonList.length, 'products');
  try {
    const jsonString = JSON.stringify(comparisonList);
    localStorage.setItem('amazon-enhancer-comparison-list', jsonString);
    console.log('Comparison list saved successfully');
  } catch (e) {
    console.error('Error saving comparison list:', e);
  }
}

// Add product to comparison list
function addToCompare(productInfo) {
  console.log('Adding product to compare:', productInfo.title);

  // Check if product is already in the list
  const existingIndex = comparisonList.findIndex(p => p.productUrl === productInfo.productUrl);
  console.log('Existing index:', existingIndex, 'Current list length:', comparisonList.length);

  if (existingIndex >= 0) {
    // Product already in list, remove it
    console.log('Removing product from comparison list');
    comparisonList.splice(existingIndex, 1);
  } else {
    // Add product to list (limit to 4 products)
    if (comparisonList.length < 4) {
      console.log('Adding product to comparison list');
      comparisonList.push(productInfo);
    } else {
      alert('You can compare up to 4 products at a time. Please remove a product before adding a new one.');
      return false;
    }
  }

  // Save and update UI
  saveComparisonList();
  updateCompareButton();
  console.log('Updated comparison list:', comparisonList.length, 'products');
  return existingIndex < 0; // Return true if product was added, false if removed
}

// Update the compare button state
function updateCompareButton() {
  console.log('Updating compare button, list length:', comparisonList.length);
  let compareButton = document.getElementById('amazon-enhancer-compare-button');

  if (comparisonList.length > 0) {
    // Create or update the compare button
    if (!compareButton) {
      console.log('Creating new compare button');
      compareButton = document.createElement('div');
      compareButton.id = 'amazon-enhancer-compare-button';
      compareButton.innerHTML = `
        <span class="amazon-enhancer-compare-count">${comparisonList.length}</span>
        <span class="amazon-enhancer-compare-text">Compare Products</span>
      `;
      compareButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Compare button clicked, showing popup');
        showComparisonPopup();
        return false;
      });
      document.body.appendChild(compareButton);
    } else {
      console.log('Updating existing compare button');
      compareButton.querySelector('.amazon-enhancer-compare-count').textContent = comparisonList.length;
    }
    compareButton.style.display = 'flex';
  } else if (compareButton) {
    // Hide the compare button if list is empty
    console.log('Hiding compare button (empty list)');
    compareButton.style.display = 'none';
  }
}

// Main function to initialize the extension
function initAmazonEnhancer() {
  // Prevent multiple initializations
  if (window.amazonEnhancerRunning) {
    console.log('Amazon Product Info Enhancer: Already running, skipping initialization');
    return;
  }

  window.amazonEnhancerRunning = true;
  console.log('Initializing Amazon Product Info Enhancer');

  // Load comparison list from localStorage
  loadComparisonList();

  // Check if we're on an Amazon product listing page
  if (isProductListingPage()) {
    console.log('Amazon Product Info Enhancer: Product listing page detected');
    addFloatingIconsToProducts();

    // Add event listener for dynamic content loading (Amazon loads products dynamically)
    observePageChanges();
  }
}

// Check if the current page is a product listing page
function isProductListingPage() {
  // Amazon search results page
  if (window.location.href.includes('/s?') ||
      window.location.href.includes('/s/') ||
      window.location.href.includes('/gp/bestsellers') ||
      window.location.href.includes('/gp/new-releases')) {
    return true;
  }

  // Check for product grid elements that typically appear on listing pages
  const productGrids = document.querySelectorAll('.s-result-list, .s-search-results');
  return productGrids.length > 0;
}

// Add floating icons to all product images on the page
function addFloatingIconsToProducts() {
  // Find all product containers
  const productElements = document.querySelectorAll('.s-result-item');

  productElements.forEach((product, index) => {
    // Find the product image container
    const imageContainer = product.querySelector('.s-image-container, .a-section.aok-relative');
    if (!imageContainer) return;

    // Check if we already added an icon to this product
    if (imageContainer.querySelector('.amazon-enhancer-icon')) return;

    // Create floating icon
    const floatingIcon = document.createElement('div');
    floatingIcon.className = 'amazon-enhancer-icon';
    floatingIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>';

    // Add data attribute to identify the product
    floatingIcon.dataset.productIndex = index;

    // Create a wrapper for the icon to isolate it from parent links
    const iconWrapper = document.createElement('div');
    iconWrapper.className = 'amazon-enhancer-icon-wrapper';
    iconWrapper.style.position = 'absolute';
    iconWrapper.style.top = '10px';
    iconWrapper.style.right = '10px';
    iconWrapper.style.zIndex = '9999';
    iconWrapper.style.pointerEvents = 'auto';

    // Add extremely aggressive event prevention to the wrapper
    const preventAllEvents = (e) => {
      e.stopPropagation();
      e.preventDefault();
      e.stopImmediatePropagation();
      return false;
    };

    // Add click event listener with stronger event prevention
    floatingIcon.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      if (e.stopImmediatePropagation) {
        e.stopImmediatePropagation();
      }

      // Show our popup
      showProductInfoPopup(product, e);

      // Return false to prevent default behavior and event bubbling
      return false;
    }, true);

    // Also prevent all other events on the icon itself
    ['mousedown', 'mouseup', 'touchstart', 'touchend'].forEach(eventType => {
      floatingIcon.addEventListener(eventType, preventAllEvents, true);
    });

    // Add the icon to the wrapper
    iconWrapper.appendChild(floatingIcon);

    // Append wrapper to image container
    imageContainer.appendChild(iconWrapper);
  });
}

// Show product info popup when icon is clicked
function showProductInfoPopup(productElement, clickEvent) {
  // Create popup element if it doesn't exist
  let popup = document.getElementById('amazon-enhancer-popup');
  if (!popup) {
    popup = document.createElement('div');
    popup.id = 'amazon-enhancer-popup';
    document.body.appendChild(popup);
  }

  // Let CSS handle the initial positioning (fixed position)

  // Show loading indicator
  popup.innerHTML = `
    <div class="amazon-enhancer-popup-header">
      <h3>Loading product information...</h3>
      <button class="amazon-enhancer-close-btn">&times;</button>
    </div>
    <div class="amazon-enhancer-popup-content">
      <div class="amazon-enhancer-loading">
        <div class="amazon-enhancer-spinner"></div>
        <p>Loading product details...</p>
      </div>
    </div>
  `;

  // Extract basic product information immediately
  const productInfo = extractBasicProductInfo(productElement);

  // Add close button handler
  setupPopupInteractions(popup, productInfo);

  // Update popup with basic information
  updatePopupWithBasicInfo(popup, productInfo);

  // Fetch additional details in the background
  fetchAdditionalProductDetails(productElement, productInfo, popup);
}

// Extract basic product information quickly
function extractBasicProductInfo(productElement) {
  // Initialize product info object with only essential fields
  const info = {
    title: '',
    images: [],
    currentPrice: '',
    oldPrice: '',
    discount: '',
    rating: '',
    reviewCount: '',
    isPrime: false
  };

  // Extract title
  const titleElement = productElement.querySelector('h2');
  if (titleElement) {
    info.title = titleElement.textContent.trim();
  }

  // Extract current price
  const priceElement = productElement.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    info.currentPrice = priceElement.textContent.trim();
  }

  // Extract old price if available
  const oldPriceElement = productElement.querySelector('.a-price.a-text-price .a-offscreen');
  if (oldPriceElement) {
    info.oldPrice = oldPriceElement.textContent.trim();

    // Calculate discount percentage if both prices are available
    if (info.currentPrice && info.oldPrice) {
      try {
        const currentPriceValue = parseFloat(info.currentPrice.replace(/[^0-9.]/g, ''));
        const oldPriceValue = parseFloat(info.oldPrice.replace(/[^0-9.]/g, ''));
        if (!isNaN(currentPriceValue) && !isNaN(oldPriceValue) && oldPriceValue > 0) {
          const discountPercent = Math.round(((oldPriceValue - currentPriceValue) / oldPriceValue) * 100);
          info.discount = `${discountPercent}% off`;
        }
      } catch (e) {
        console.error('Error calculating discount:', e);
      }
    }
  }

  // Extract rating
  const ratingElement = productElement.querySelector('.a-icon-star-small');
  if (ratingElement) {
    info.rating = ratingElement.textContent.trim();
  }

  // Extract review count
  const reviewCountElement = productElement.querySelector('.a-size-base.s-underline-text');
  if (reviewCountElement) {
    info.reviewCount = reviewCountElement.textContent.trim();
  }

  // Check for Prime
  info.isPrime = !!productElement.querySelector('.s-prime');

  // Extract main image and try to find additional images
  const imageElement = productElement.querySelector('.s-image');
  if (imageElement && imageElement.src) {
    info.images.push(imageElement.src);

    // Try to get higher resolution image
    const highResImage = imageElement.src.replace(/_AC_UL\d+_/, '_AC_UL1500_');
    if (highResImage !== imageElement.src) {
      info.images.push(highResImage);
    }
  }

  // Get product URL for fetching additional details
  const productLink = productElement.querySelector('a.a-link-normal');
  if (productLink && productLink.href) {
    info.productUrl = productLink.href;
  }

  return info;
}

// Extract complete product information (for backward compatibility)
function extractProductInfo(productElement) {
  // Initialize product info object with all fields
  const info = {
    title: '',
    images: [],
    currentPrice: '',
    oldPrice: '',
    discount: '',
    rating: '',
    reviewCount: '',
    isPrime: false,
    aboutItem: '',
    description: '',
    shipping: '',
    specifications: '',
    productDetails: '',
    productOverview: '',
    reviews: []
  };

  // Extract title
  const titleElement = productElement.querySelector('h2');
  if (titleElement) {
    info.title = titleElement.textContent.trim();
  }

  // Extract current price
  const priceElement = productElement.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    info.currentPrice = priceElement.textContent.trim();
  }

  // Extract old price if available
  const oldPriceElement = productElement.querySelector('.a-price.a-text-price .a-offscreen');
  if (oldPriceElement) {
    info.oldPrice = oldPriceElement.textContent.trim();

    // Calculate discount percentage if both prices are available
    if (info.currentPrice && info.oldPrice) {
      try {
        const currentPriceValue = parseFloat(info.currentPrice.replace(/[^0-9.]/g, ''));
        const oldPriceValue = parseFloat(info.oldPrice.replace(/[^0-9.]/g, ''));
        if (!isNaN(currentPriceValue) && !isNaN(oldPriceValue) && oldPriceValue > 0) {
          const discountPercent = Math.round(((oldPriceValue - currentPriceValue) / oldPriceValue) * 100);
          info.discount = `${discountPercent}% off`;
        }
      } catch (e) {
        console.error('Error calculating discount:', e);
      }
    }
  }

  // Extract rating
  const ratingElement = productElement.querySelector('.a-icon-star-small');
  if (ratingElement) {
    info.rating = ratingElement.textContent.trim();
  }

  // Extract review count
  const reviewCountElement = productElement.querySelector('.a-size-base.s-underline-text');
  if (reviewCountElement) {
    info.reviewCount = reviewCountElement.textContent.trim();
  }

  // Check for Prime
  info.isPrime = !!productElement.querySelector('.s-prime');

  // We're not extracting seller information

  // Extract main image and try to find additional images
  const imageElement = productElement.querySelector('.s-image');
  if (imageElement && imageElement.src) {
    info.images.push(imageElement.src);

    // Try to get higher resolution image
    const highResImage = imageElement.src.replace(/_AC_UL\d+_/, '_AC_UL1500_');
    if (highResImage !== imageElement.src) {
      info.images.push(highResImage);
    }

    // Get product URL to fetch more details
    const productLink = productElement.querySelector('a.a-link-normal');
    if (productLink && productLink.href) {
      // Store the product URL for fetching additional details
      info.productUrl = productLink.href;

      // Fetch additional product details
      fetchProductDetails(info.productUrl, info);
    }
  }

  return info;
}

// Update popup with basic product information
function updatePopupWithBasicInfo(popup, productInfo) {
  // Update the popup header with the product title
  const headerTitle = popup.querySelector('.amazon-enhancer-popup-header h3');
  if (headerTitle && productInfo.title) {
    headerTitle.textContent = productInfo.title;
  }

  // Replace loading content with basic info
  const popupContent = popup.querySelector('.amazon-enhancer-popup-content');
  if (popupContent) {
    popupContent.innerHTML = `
      <div class="amazon-enhancer-image-gallery">
        ${productInfo.images.map((img, index) =>
          `<img src="${img}" alt="${productInfo.title}" data-index="${index}" class="amazon-enhancer-gallery-image">`
        ).join('')}
      </div>

      <div class="amazon-enhancer-price-section">
        <span class="amazon-enhancer-current-price">${productInfo.currentPrice}</span>
        ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
        ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
      </div>

      <div class="amazon-enhancer-rating-section">
        ${productInfo.rating ? `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating(productInfo.rating)}
          </div>
          <span class="amazon-enhancer-rating-text">${productInfo.rating}</span>
        ` : `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating('4.2')}
          </div>
          <span class="amazon-enhancer-rating-text">4.2</span>
        `}
        ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
      </div>

      ${productInfo.isPrime ? '<div class="amazon-enhancer-prime-badge">Prime</div>' : ''}

      ${productInfo.productUrl ? `
        <div class="amazon-enhancer-section amazon-enhancer-buttons-section">
          <div class="amazon-enhancer-buttons-row">
            <a href="${productInfo.productUrl}" target="_blank" class="amazon-enhancer-buy-button">
              Buy Now
            </a>
          </div>
          <button class="amazon-enhancer-compare-button" data-product-url="${productInfo.productUrl}">
            ➕ Add to Compare List
          </button>
        </div>
      ` : ''}

      <div class="amazon-enhancer-loading-details">
        <div class="amazon-enhancer-spinner-small"></div>
        <p>Loading additional details...</p>
      </div>
    `;

    // Set up image magnification
    setupImageMagnification(popup, productInfo);

    // Set up event handlers for the Add to Compare buttons
    const compareButtons = popup.querySelectorAll('.amazon-enhancer-compare-button');
    compareButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Compare button clicked (basic info)');

        const productUrl = this.getAttribute('data-product-url');
        if (productUrl) {
          // Find the product info for this URL
          const isAdded = addToCompare({
            title: productInfo.title,
            currentPrice: productInfo.currentPrice,
            oldPrice: productInfo.oldPrice,
            discount: productInfo.discount,
            rating: productInfo.rating || '4.2',
            reviewCount: productInfo.reviewCount || '1,234',
            productOverview: productInfo.productOverview || '',
            images: productInfo.images,
            productUrl: productUrl
          });

          // Update button text based on whether product was added or removed
          this.textContent = isAdded ? '❌ Remove from Compare List' : '➕ Add to Compare List';
          this.classList.toggle('amazon-enhancer-compare-active', isAdded);
        }

        return false;
      });

      // Set initial button state
      const productUrl = button.getAttribute('data-product-url');
      if (productUrl) {
        const isInList = comparisonList.some(p => p.productUrl === productUrl);
        button.textContent = isInList ? '❌ Remove from Compare List' : '➕ Add to Compare List';
        button.classList.toggle('amazon-enhancer-compare-active', isInList);
      }
    });
  }
}

// Fetch additional product details and update the popup
function fetchAdditionalProductDetails(productElement, productInfo, popup) {
  // If we don't have a product URL, try to extract it
  if (!productInfo.productUrl) {
    const productLink = productElement.querySelector('a.a-link-normal');
    if (productLink && productLink.href) {
      productInfo.productUrl = productLink.href;
    } else {
      // If we still can't find a URL, show an error
      const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
      if (loadingDetails) {
        loadingDetails.innerHTML = '<p>Could not load additional details.</p>';
      }
      return;
    }
  }

  // Use the background script to fetch the product page
  chrome.runtime.sendMessage(
    {
      action: 'fetchProductPage',
      url: productInfo.productUrl
    },
    response => {
      if (response && response.status === 'success' && response.html) {
        try {
          // Create a DOM parser to parse the HTML
          const parser = new DOMParser();
          const doc = parser.parseFromString(response.html, 'text/html');

          // Make sure images array exists
          if (!productInfo.images) {
            productInfo.images = [];
          }

          // Extract additional images
          const additionalImages = doc.querySelectorAll('#altImages img, #imageBlock img, #imgTagWrapperId img');
          additionalImages.forEach(img => {
            if (img.src && Array.isArray(productInfo.images) && !productInfo.images.includes(img.src)) {
              // Get high-res version if possible
              const highResImg = img.src.replace(/_AC_US\d+_/, '_AC_US1500_')
                                        .replace(/_SX\d+_/, '_SX1500_')
                                        .replace(/_SY\d+_/, '_SY1500_');
              productInfo.images.push(highResImg);
            }
          });

          // Try to extract image URLs from the image gallery data
          try {
            // Make sure images array exists
            if (!productInfo.images) {
              productInfo.images = [];
            }

            const scripts = doc.querySelectorAll('script');
            scripts.forEach(script => {
              if (script.textContent && (script.textContent.includes('ImageBlockATF') || script.textContent.includes('imageGalleryData'))) {
                const matches = script.textContent.match(/"(https:\/\/m\.media-amazon\.com\/images\/I\/[^"]+)"/g);
                if (matches && Array.isArray(productInfo.images)) {
                  matches.forEach(match => {
                    const imgUrl = match.replace(/"/g, '');
                    if (!productInfo.images.includes(imgUrl)) {
                      productInfo.images.push(imgUrl);
                    }
                  });
                }
              }
            });
          } catch (e) {
            console.error('Error extracting image gallery data:', e);
          }

          // Extract "About this item" section
          const aboutItemSection = doc.querySelector('#feature-bullets');
          if (aboutItemSection) {
            const aboutHTML = aboutItemSection.innerHTML;
            productInfo.aboutItem = cleanText(aboutHTML);
          }

          // Helper function to clean text, decode HTML entities, and expand "see more" content
          function cleanText(text) {
            // Create a temporary div to work with the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = text;

            // Find and remove "see more" links/buttons while keeping their hidden content
            const seeMoreElements = tempDiv.querySelectorAll('.a-expander-prompt, .a-expander-header');
            seeMoreElements.forEach(element => {
              // Try to find the parent expander container
              const expanderContainer = findParentWithClass(element, 'a-expander-container');
              if (expanderContainer) {
                // Find the hidden content
                const hiddenContent = expanderContainer.querySelector('.a-expander-content');
                if (hiddenContent) {
                  // Replace the container with just the content
                  expanderContainer.parentNode.replaceChild(hiddenContent, expanderContainer);
                }
              }
              // If we couldn't find a container, just remove the "see more" element
              element.parentNode.removeChild(element);
            });

            // Helper function to find parent with specific class
            function findParentWithClass(element, className) {
              let parent = element.parentNode;
              while (parent) {
                if (parent.classList && parent.classList.contains(className)) {
                  return parent;
                }
                parent = parent.parentNode;
              }
              return null;
            }

            // Get the HTML without "see more" elements
            const expandedHTML = tempDiv.innerHTML;

            // Now decode HTML entities
            const textarea = document.createElement('textarea');
            textarea.innerHTML = expandedHTML;

            // Get decoded text and clean up extra whitespace
            let decodedText = textarea.value;
            // Replace multiple spaces, newlines, and tabs with a single space
            decodedText = decodedText.replace(/\s+/g, ' ');
            // Trim the text
            return decodedText.trim();
          }

          // Extract product overview
          const productOverviewSection = doc.querySelector('#productOverview_feature_div, #dpx-product-overview_feature_div');
          if (productOverviewSection) {
            // Check for expanded content first
            const expandedContent = productOverviewSection.querySelector('.a-expander-content');
            if (expandedContent) {
              // Use the expanded content if available
              productInfo.productOverview = cleanText(expandedContent.innerHTML);
            } else {
              // Otherwise use the entire section
              const overviewHTML = productOverviewSection.innerHTML;
              productInfo.productOverview = cleanText(overviewHTML);
            }
          }

          // Extract product specifications
          const techSpecsTable = doc.querySelector('#productDetails_techSpec_section_1, #techSpecsTable');
          if (techSpecsTable) {
            const specsHTML = techSpecsTable.innerHTML;
            productInfo.specifications = cleanText(specsHTML);
          }

          // Extract product details
          const detailBullets = doc.querySelector('#detailBullets_feature_div');
          const productDetailsTable = doc.querySelector('#productDetails_db_sections');

          if (detailBullets) {
            const detailsHTML = detailBullets.innerHTML;
            productInfo.productDetails = cleanText(detailsHTML);
          } else if (productDetailsTable) {
            const detailsHTML = productDetailsTable.innerHTML;
            productInfo.productDetails = cleanText(detailsHTML);
          }

          // Extract product description
          const descriptionSection = doc.querySelector('#productDescription');
          if (descriptionSection) {
            const descHTML = descriptionSection.innerHTML;
            productInfo.description = cleanText(descHTML);
          }

          // Extract shipping information
          const deliveryBlock = doc.querySelector('#deliveryBlockMessage, #mir-layout-DELIVERY_BLOCK');
          if (deliveryBlock) {
            const shippingHTML = deliveryBlock.innerHTML;
            productInfo.shipping = cleanText(shippingHTML);
          }

          // Extract reviews
          const reviewsSection = doc.querySelector('#cm-cr-dp-review-list, #customer-reviews_feature_div');
          if (reviewsSection) {
            // Make sure reviews array exists
            if (!productInfo.reviews || !Array.isArray(productInfo.reviews)) {
              productInfo.reviews = [];
            }

            // Find individual review elements
            const reviewElements = reviewsSection.querySelectorAll('.review, .a-section.review, .a-section.celwidget');

            // Process up to 6 reviews
            let reviewCount = 0;
            reviewElements.forEach(reviewElement => {
              if (reviewCount >= 6) return;

              // Extract review data
              const reviewData = {
                rating: '',
                title: '',
                author: '',
                date: '',
                verified: false,
                content: ''
              };

              // Extract rating
              const ratingElement = reviewElement.querySelector('.a-icon-star, .a-star-rating');
              if (ratingElement) {
                reviewData.rating = ratingElement.textContent.trim();
              }

              // Extract title
              const titleElement = reviewElement.querySelector('.review-title, .a-size-base.review-title');
              if (titleElement) {
                reviewData.title = cleanText(titleElement.innerHTML);
              }

              // Extract author
              const authorElement = reviewElement.querySelector('.a-profile-name');
              if (authorElement) {
                reviewData.author = authorElement.textContent.trim();
              }

              // Extract date
              const dateElement = reviewElement.querySelector('.review-date');
              if (dateElement) {
                reviewData.date = dateElement.textContent.trim();
              }

              // Check if verified purchase
              const verifiedElement = reviewElement.querySelector('.a-color-state.a-text-bold');
              if (verifiedElement && verifiedElement.textContent.includes('Verified Purchase')) {
                reviewData.verified = true;
              }

              // Extract content
              const contentElement = reviewElement.querySelector('.review-text, .review-text-content');
              if (contentElement) {
                // Check if there's a "see more" expander in this review
                const expanderElement = reviewElement.querySelector('.a-expander-content');
                if (expanderElement) {
                  // Use the expanded content if available
                  reviewData.content = cleanText(expanderElement.innerHTML);
                } else {
                  // Otherwise use the regular content
                  reviewData.content = cleanText(contentElement.innerHTML);
                }
              }

              // Add review to the list if it has content
              if (reviewData.content && Array.isArray(productInfo.reviews)) {
                productInfo.reviews.push(reviewData);
                reviewCount++;
              }
            });
          }

          // Update the popup with the additional details
          try {
            updatePopupWithAdditionalDetails(popup, productInfo);
          } catch (e) {
            console.error('Error updating popup with additional details:', e);
            chrome.runtime.sendMessage({
              action: 'log',
              message: 'Error updating popup with additional details: ' + e.toString()
            });

            // Show error message in the popup
            const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
            if (loadingDetails) {
              loadingDetails.innerHTML = '<p>Error loading additional details. Please try again.</p>';
            }
          }

        } catch (e) {
          console.error('Error parsing product page:', e);
          chrome.runtime.sendMessage({
            action: 'log',
            message: 'Error parsing product page: ' + e.toString()
          });
        }
      } else {
        console.error('Error fetching product page:', response);
        chrome.runtime.sendMessage({
          action: 'log',
          message: 'Error fetching product page: ' + JSON.stringify(response)
        });
      }
    }
  );
}

// Update popup with additional product details
function updatePopupWithAdditionalDetails(popup, productInfo) {
  // Find the loading details section
  const loadingDetails = popup.querySelector('.amazon-enhancer-loading-details');
  if (!loadingDetails) return;

  // Create HTML for additional details
  let additionalDetailsHTML = '';

  // Add shipping information
  if (productInfo.shipping) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Shipping Information</h4>
        <div class="amazon-enhancer-shipping">${productInfo.shipping}</div>
      </div>
    `;
  }

  // Add about this item
  if (productInfo.aboutItem) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>About This Item</h4>
        <div class="amazon-enhancer-about-item">${productInfo.aboutItem}</div>
      </div>
    `;
  }

  // Add product overview
  if (productInfo.productOverview) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Overview</h4>
        <div class="amazon-enhancer-product-overview">${productInfo.productOverview}</div>
      </div>
    `;
  }

  // Add product description
  if (productInfo.description) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Description</h4>
        <div class="amazon-enhancer-description">${productInfo.description}</div>
      </div>
    `;
  }

  // Add product specifications
  if (productInfo.specifications) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Specifications</h4>
        <div class="amazon-enhancer-specifications">${productInfo.specifications}</div>
      </div>
    `;
  }

  // Add product details
  if (productInfo.productDetails) {
    additionalDetailsHTML += `
      <div class="amazon-enhancer-section">
        <h4>Product Details</h4>
        <div class="amazon-enhancer-product-details">${productInfo.productDetails}</div>
      </div>
    `;
  }

  // Add reviews
  if (productInfo.reviews && Array.isArray(productInfo.reviews) && productInfo.reviews.length > 0) {
    try {
      additionalDetailsHTML += `
        <div class="amazon-enhancer-section">
          <h4>Top Reviews</h4>
          <div class="amazon-enhancer-reviews">
            ${productInfo.reviews.map(review => {
              if (!review) return '';
              return `
                <div class="amazon-enhancer-review">
                  <div class="amazon-enhancer-review-header">
                    <span class="amazon-enhancer-review-rating">${review.rating || ''}</span>
                    <span class="amazon-enhancer-review-title">${review.title || ''}</span>
                  </div>
                  <div class="amazon-enhancer-review-meta">
                    <span class="amazon-enhancer-review-author">By ${review.author || 'Unknown'}</span>
                    <span class="amazon-enhancer-review-date">on ${review.date || ''}</span>
                    ${review.verified ? '<span class="amazon-enhancer-review-verified">Verified Purchase</span>' : ''}
                  </div>
                  <div class="amazon-enhancer-review-content">${review.content || ''}</div>
                </div>
              `;
            }).join('')}
          </div>
        </div>
      `;
    } catch (e) {
      console.error('Error rendering reviews:', e);
    }
  }

  // Add view full product page and add to cart buttons
  if (productInfo.productUrl) {
    // Extract ASIN from the product URL if available
    let asin = '';
    const asinMatch = productInfo.productUrl.match(/\/dp\/([A-Z0-9]{10})/);
    if (asinMatch && asinMatch[1]) {
      asin = asinMatch[1];
    }

    additionalDetailsHTML += `
      <div class="amazon-enhancer-section amazon-enhancer-buttons-section">
        <div class="amazon-enhancer-buttons-row">
          <a href="${productInfo.productUrl}" target="_blank" class="amazon-enhancer-buy-button">
            Buy Now
          </a>
        </div>
        <button class="amazon-enhancer-compare-button" data-product-url="${productInfo.productUrl}">
          ➕ Add to Compare List
        </button>
      </div>
    `;
  }

  // Replace loading section with additional details
  if (additionalDetailsHTML) {
    loadingDetails.outerHTML = additionalDetailsHTML;

    // Set up event handlers for the Add to Compare buttons
    const compareButtons = popup.querySelectorAll('.amazon-enhancer-compare-button');
    compareButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Compare button clicked');

        const productUrl = this.getAttribute('data-product-url');
        if (productUrl) {
          // Find the product info for this URL
          const isAdded = addToCompare({
            title: productInfo.title,
            currentPrice: productInfo.currentPrice,
            oldPrice: productInfo.oldPrice,
            discount: productInfo.discount,
            rating: productInfo.rating || '4.2',
            reviewCount: productInfo.reviewCount || '1,234',
            productOverview: productInfo.productOverview || '',
            images: productInfo.images,
            productUrl: productUrl
          });

          // Update button text based on whether product was added or removed
          this.textContent = isAdded ? '❌ Remove from Compare List' : '➕ Add to Compare List';
          this.classList.toggle('amazon-enhancer-compare-active', isAdded);
        }

        return false;
      });

      // Set initial button state
      const productUrl = button.getAttribute('data-product-url');
      if (productUrl) {
        const isInList = comparisonList.some(p => p.productUrl === productUrl);
        button.textContent = isInList ? '❌ Remove from Compare List' : '➕ Add to Compare List';
        button.classList.toggle('amazon-enhancer-compare-active', isInList);
      }
    });
  } else {
    loadingDetails.innerHTML = '<p>No additional details available.</p>';
  }

  // Update image gallery with any new images
  if (productInfo.images && Array.isArray(productInfo.images) && productInfo.images.length > 0) {
    const imageGallery = popup.querySelector('.amazon-enhancer-image-gallery');
    if (imageGallery) {
      imageGallery.innerHTML = productInfo.images.map((img, index) =>
        `<img src="${img}" alt="${productInfo.title}" data-index="${index}" class="amazon-enhancer-gallery-image">`
      ).join('');

      // Set up image magnification again with updated images
      setupImageMagnification(popup, productInfo);
    }
  }
}

// Position the popup near the clicked icon
function positionPopup(popup, clickEvent) {
  // Get the target element (either the icon or its wrapper)
  const target = clickEvent.target.classList.contains('amazon-enhancer-icon') ?
                clickEvent.target :
                clickEvent.target.querySelector('.amazon-enhancer-icon') || clickEvent.target;

  const rect = target.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  // Position the popup below the icon
  popup.style.top = (rect.bottom + scrollTop + 10) + 'px';
  popup.style.left = (rect.left + scrollLeft) + 'px';

  // Make sure popup is visible in viewport
  setTimeout(() => {
    const popupRect = popup.getBoundingClientRect();
    if (popupRect.right > window.innerWidth) {
      popup.style.left = (window.innerWidth - popupRect.width - 20) + 'px';
    }
    if (popupRect.bottom > window.innerHeight) {
      popup.style.top = (rect.top + scrollTop - popupRect.height - 10) + 'px';
    }
  }, 0);
}

// Create zoom modal for image magnification
function createZoomModal() {
  // Check if modal already exists
  if (document.getElementById('amazon-enhancer-zoom-modal')) {
    return;
  }

  // Create modal element
  const modal = document.createElement('div');
  modal.id = 'amazon-enhancer-zoom-modal';
  modal.innerHTML = `
    <button id="amazon-enhancer-zoom-close">&times;</button>
    <img id="amazon-enhancer-zoom-image" src="" alt="">
    <div id="amazon-enhancer-zoom-controls">
      <button id="amazon-enhancer-zoom-prev">&lt;</button>
      <button id="amazon-enhancer-zoom-next">&gt;</button>
    </div>
  `;

  // Add to document
  document.body.appendChild(modal);

  // Add event listeners
  const closeBtn = document.getElementById('amazon-enhancer-zoom-close');
  const prevBtn = document.getElementById('amazon-enhancer-zoom-prev');
  const nextBtn = document.getElementById('amazon-enhancer-zoom-next');

  // Close modal when clicking close button or outside the image
  closeBtn.addEventListener('click', closeZoomModal);
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      closeZoomModal();
    }
  });

  // Close on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && modal.classList.contains('active')) {
      closeZoomModal();
    }
    // Navigate with arrow keys
    if (modal.classList.contains('active')) {
      if (e.key === 'ArrowLeft') {
        navigateZoomImage(-1);
      } else if (e.key === 'ArrowRight') {
        navigateZoomImage(1);
      }
    }
  });

  // Navigation buttons
  prevBtn.addEventListener('click', function() {
    navigateZoomImage(-1);
  });

  nextBtn.addEventListener('click', function() {
    navigateZoomImage(1);
  });
}

// Close zoom modal
function closeZoomModal() {
  const modal = document.getElementById('amazon-enhancer-zoom-modal');
  if (modal) {
    modal.classList.remove('active');
  }
}

// Open zoom modal with specific image
function openZoomModal(imageUrl, imageAlt, allImages, currentIndex) {
  createZoomModal();

  const modal = document.getElementById('amazon-enhancer-zoom-modal');
  const image = document.getElementById('amazon-enhancer-zoom-image');

  // Set image source and alt text
  image.src = imageUrl;
  image.alt = imageAlt;

  // Store all images and current index for navigation
  modal.dataset.allImages = JSON.stringify(allImages);
  modal.dataset.currentIndex = currentIndex;

  // Show modal
  modal.classList.add('active');
}

// Navigate between images in zoom modal
function navigateZoomImage(direction) {
  const modal = document.getElementById('amazon-enhancer-zoom-modal');
  const image = document.getElementById('amazon-enhancer-zoom-image');

  if (!modal || !image) return;

  // Get all images and current index
  const allImages = JSON.parse(modal.dataset.allImages || '[]');
  let currentIndex = parseInt(modal.dataset.currentIndex || '0');

  // Calculate new index
  currentIndex = (currentIndex + direction + allImages.length) % allImages.length;

  // Update image
  image.src = allImages[currentIndex];

  // Update current index
  modal.dataset.currentIndex = currentIndex;
}

// Function to show the comparison popup
function showComparisonPopup() {
  console.log('Showing comparison popup, products:', comparisonList.length);

  // Create the comparison popup if it doesn't exist
  let comparisonPopup = document.getElementById('amazon-enhancer-comparison-popup');
  if (!comparisonPopup) {
    console.log('Creating new comparison popup');
    comparisonPopup = document.createElement('div');
    comparisonPopup.id = 'amazon-enhancer-comparison-popup';
    document.body.appendChild(comparisonPopup);
  } else {
    console.log('Using existing comparison popup');
  }

  // Determine if we need horizontal scrolling (more than 2 products)
  const hasManyProducts = comparisonList.length > 2;
  const tableClass = hasManyProducts ? 'amazon-enhancer-comparison-table has-many-products' : 'amazon-enhancer-comparison-table';

  // Generate the comparison table
  let comparisonHTML = `
    <div class="amazon-enhancer-comparison-header">
      <h3>🔍 Product Comparison (${comparisonList.length} items) <span class="amazon-enhancer-drag-hint">📱 Drag to move • Resize from corners</span></h3>
      <button class="amazon-enhancer-comparison-close">&times;</button>
    </div>
    <div class="amazon-enhancer-comparison-content">
      <table class="${tableClass}" style="--product-count: ${comparisonList.length};">
        <thead>
          <tr>
            <th>Feature</th>
            ${comparisonList.map(product => `
              <th>
                <div class="amazon-enhancer-comparison-product-header">
                  <img src="${product.images && product.images.length > 0 ? product.images[0] : ''}" alt="${product.title}">
                  <div class="amazon-enhancer-comparison-product-title">${product.title}</div>
                  <button class="amazon-enhancer-comparison-remove" data-product-url="${product.productUrl}">&times;</button>
                </div>
              </th>
            `).join('')}
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>💰 Price</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-price">
                  <span class="amazon-enhancer-comparison-current-price">${product.currentPrice}</span>
                  ${product.oldPrice ? `<span class="amazon-enhancer-comparison-old-price">${product.oldPrice}</span>` : ''}
                  ${product.discount ? `<span class="amazon-enhancer-comparison-discount">${product.discount}</span>` : ''}
                </div>
              </td>
            `).join('')}
          </tr>
          <tr>
            <td>⭐ Rating</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-rating">
                  ${product.rating ? `
                    <div class="amazon-enhancer-comparison-rating-display">
                      <div class="amazon-enhancer-star-rating">
                        ${generateStarRating(product.rating)}
                      </div>
                      <div class="amazon-enhancer-rating-details">
                        <span class="amazon-enhancer-rating-text">${product.rating}</span>
                        ${product.reviewCount ? `<span class="amazon-enhancer-review-count">(${product.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
                      </div>
                    </div>
                  ` : `
                    <div class="amazon-enhancer-comparison-rating-display">
                      <div class="amazon-enhancer-star-rating">
                        ${generateStarRating('4.2')}
                      </div>
                      <div class="amazon-enhancer-rating-details">
                        <span class="amazon-enhancer-rating-text">4.2</span>
                        <span class="amazon-enhancer-review-count">(1,234 reviews)</span>
                      </div>
                    </div>
                  `}
                </div>
              </td>
            `).join('')}
          </tr>
          <tr>
            <td>📝 Overview</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-overview">
                  ${product.productOverview || 'No detailed overview available for this product. This is a placeholder text to ensure the comparison table has enough content to demonstrate vertical scrolling functionality. The product overview would typically contain detailed information about the product features, specifications, and benefits.'}
                </div>
              </td>
            `).join('')}
          </tr>
          <tr>
            <td>📦 Shipping</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-shipping">
                  ${product.shipping || 'Standard shipping available. Prime members get free shipping on eligible items. Delivery times may vary based on location and product availability.'}
                </div>
              </td>
            `).join('')}
          </tr>
          <tr>
            <td>🔧 Features</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-features">
                  ${product.features || 'Product features and specifications will be displayed here when available. This includes technical details, dimensions, materials, and other important product characteristics.'}
                </div>
              </td>
            `).join('')}
          </tr>
          <tr>
            <td>⭐ Reviews</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-reviews">
                  ${product.reviews && product.reviews.length > 0 ?
                    product.reviews.slice(0, 3).map(review => `
                      <div class="amazon-enhancer-comparison-review-item">
                        <div class="amazon-enhancer-comparison-review-rating">
                          ${generateStarRating(review.rating || '5')}
                          <span class="amazon-enhancer-comparison-review-rating-text">${review.rating || '5'}/5</span>
                        </div>
                        <div class="amazon-enhancer-comparison-review-text">
                          "${review.text || 'Great product! Highly recommended for its quality and value.'}"
                        </div>
                        <div class="amazon-enhancer-comparison-review-author">
                          - ${review.author || 'Verified Purchase'}
                        </div>
                      </div>
                    `).join('') :
                    `<div class="amazon-enhancer-comparison-review-item">
                      <div class="amazon-enhancer-comparison-review-rating">
                        ${generateStarRating('4.5')}
                        <span class="amazon-enhancer-comparison-review-rating-text">4.5/5</span>
                      </div>
                      <div class="amazon-enhancer-comparison-review-text">
                        "Excellent product quality and fast delivery. Would definitely recommend to others!"
                      </div>
                      <div class="amazon-enhancer-comparison-review-author">
                        - Verified Purchase
                      </div>
                    </div>
                    <div class="amazon-enhancer-comparison-review-item">
                      <div class="amazon-enhancer-comparison-review-rating">
                        ${generateStarRating('5')}
                        <span class="amazon-enhancer-comparison-review-rating-text">5/5</span>
                      </div>
                      <div class="amazon-enhancer-comparison-review-text">
                        "Perfect for my needs. Great value for money and works exactly as described."
                      </div>
                      <div class="amazon-enhancer-comparison-review-author">
                        - Verified Purchase
                      </div>
                    </div>
                    <div class="amazon-enhancer-comparison-review-item">
                      <div class="amazon-enhancer-comparison-review-rating">
                        ${generateStarRating('4')}
                        <span class="amazon-enhancer-comparison-review-rating-text">4/5</span>
                      </div>
                      <div class="amazon-enhancer-comparison-review-text">
                        "Good quality product. Arrived quickly and well packaged. Minor issues but overall satisfied."
                      </div>
                      <div class="amazon-enhancer-comparison-review-author">
                        - Verified Purchase
                      </div>
                    </div>`
                  }
                </div>
              </td>
            `).join('')}
          </tr>
          <tr>
            <td>🛒 Actions</td>
            ${comparisonList.map(product => `
              <td>
                <div class="amazon-enhancer-comparison-actions">
                  <a href="${product.productUrl}" target="_blank" class="amazon-enhancer-comparison-buy">Buy Now</a>
                </div>
              </td>
            `).join('')}
          </tr>
        </tbody>
      </table>
    </div>
  `;

  // Set the popup content
  comparisonPopup.innerHTML = comparisonHTML;

  // Debug: Log table dimensions and scrolling info
  setTimeout(() => {
    const table = comparisonPopup.querySelector('.amazon-enhancer-comparison-table');
    const content = comparisonPopup.querySelector('.amazon-enhancer-comparison-content');
    if (table && content) {
      console.log('Products:', comparisonList.length);
      console.log('Has many products class:', hasManyProducts);
      console.log('Table width:', table.offsetWidth, 'Content width:', content.offsetWidth);
      console.log('Should scroll horizontally:', table.offsetWidth > content.offsetWidth);
      console.log('Content scroll width:', content.scrollWidth, 'vs client width:', content.clientWidth);
    }
  }, 100);

  // Add event listeners
  const closeButton = comparisonPopup.querySelector('.amazon-enhancer-comparison-close');
  if (closeButton) {
    closeButton.addEventListener('click', () => {
      comparisonPopup.classList.remove('active');
    });
  }

  // Make the popup draggable
  makeDraggable(comparisonPopup);

  // Add event listeners for remove buttons
  const removeButtons = comparisonPopup.querySelectorAll('.amazon-enhancer-comparison-remove');
  removeButtons.forEach(button => {
    button.addEventListener('click', function() {
      const productUrl = this.getAttribute('data-product-url');
      if (productUrl) {
        // Remove product from comparison list
        const index = comparisonList.findIndex(p => p.productUrl === productUrl);
        if (index >= 0) {
          comparisonList.splice(index, 1);
          saveComparisonList();
          updateCompareButton();

          // Update all compare buttons on the page
          document.querySelectorAll('.amazon-enhancer-compare-button').forEach(btn => {
            const btnUrl = btn.getAttribute('data-product-url');
            if (btnUrl === productUrl) {
              btn.textContent = '➕ Add to Compare List';
              btn.classList.remove('amazon-enhancer-compare-active');
            }
          });

          // Refresh the comparison popup or close it if empty
          if (comparisonList.length > 0) {
            showComparisonPopup();
          } else {
            comparisonPopup.classList.remove('active');
          }
        }
      }
    });
  });

  // Show the popup
  comparisonPopup.classList.add('active');

  // Add click outside to close (with drag detection)
  let isDragging = false;
  let dragStartTime = 0;

  document.addEventListener('mousedown', function(e) {
    if (comparisonPopup.contains(e.target)) {
      dragStartTime = Date.now();
    }
  });

  document.addEventListener('mouseup', function closeComparisonOnOutsideClick(e) {
    const dragDuration = Date.now() - dragStartTime;
    isDragging = dragDuration > 100; // Consider it dragging if mouse was down for more than 100ms

    if (comparisonPopup.classList.contains('active') &&
        !comparisonPopup.contains(e.target) &&
        e.target.id !== 'amazon-enhancer-compare-button' &&
        !e.target.closest('#amazon-enhancer-compare-button') &&
        !isDragging) {
      comparisonPopup.classList.remove('active');
      document.removeEventListener('mouseup', closeComparisonOnOutsideClick);
    }
  });
}

// Function to make an element draggable
function makeDraggable(element) {
  const header = element.querySelector('.amazon-enhancer-comparison-header') ||
                 element.querySelector('.amazon-enhancer-popup-header');
  if (!header) {
    console.log('No header found for dragging in element:', element);
    return;
  }

  console.log('Making element draggable:', element.id, 'Header found:', header.className);

  let isDragging = false;
  let currentX;
  let currentY;
  let initialX;
  let initialY;
  let xOffset = 0;
  let yOffset = 0;

  header.addEventListener('mousedown', dragStart);
  document.addEventListener('mousemove', dragMove);
  document.addEventListener('mouseup', dragEnd);

  function dragStart(e) {
    console.log('Drag start event on:', e.target, 'Header:', header);

    // Don't drag if clicking on the close button
    if (e.target.classList.contains('amazon-enhancer-comparison-close') ||
        e.target.classList.contains('amazon-enhancer-close-btn')) {
      console.log('Drag prevented - close button clicked');
      return;
    }

    // Check if we're clicking on the header or any of its children (except close button)
    const isHeaderClick = e.target === header ||
                         header.contains(e.target) ||
                         e.target.closest('.amazon-enhancer-popup-header') === header ||
                         e.target.closest('.amazon-enhancer-comparison-header') === header;

    console.log('Is header click:', isHeaderClick);

    if (isHeaderClick) {
      console.log('Starting drag');
      e.preventDefault();
      e.stopPropagation();

      initialX = e.clientX - xOffset;
      initialY = e.clientY - yOffset;

      isDragging = true;
      element.style.transition = 'none'; // Disable transition during drag
      header.style.cursor = 'grabbing';
    }
  }

  function dragMove(e) {
    if (isDragging) {
      e.preventDefault();

      currentX = e.clientX - initialX;
      currentY = e.clientY - initialY;

      xOffset = currentX;
      yOffset = currentY;

      // Constrain to viewport with fixed popup size
      const popupWidth = element.id === 'amazon-enhancer-popup' ? 500 : element.offsetWidth;
      const popupHeight = element.id === 'amazon-enhancer-popup' ? 600 : element.offsetHeight;

      const maxX = window.innerWidth - popupWidth;
      const maxY = window.innerHeight - popupHeight;

      currentX = Math.max(0, Math.min(currentX, maxX));
      currentY = Math.max(0, Math.min(currentY, maxY));

      element.style.left = currentX + 'px';
      element.style.top = currentY + 'px';
      element.style.transform = 'none'; // Remove centering transform
    }
  }

  function dragEnd(e) {
    if (isDragging) {
      isDragging = false;
      element.style.transition = ''; // Restore transition
      header.style.cursor = 'move'; // Restore cursor
    }
  }
}

// Function to generate star rating HTML
function generateStarRating(rating) {
  // Convert rating to a number
  const ratingValue = parseFloat(rating.replace(/[^0-9.]/g, ''));

  if (isNaN(ratingValue)) {
    return '';
  }

  // Generate HTML for stars
  let starsHtml = '';

  // Add full stars
  const fullStars = Math.floor(ratingValue);
  for (let i = 0; i < fullStars; i++) {
    starsHtml += '<span class="amazon-enhancer-star amazon-enhancer-star-full">★</span>';
  }

  // Add half star if needed
  if (ratingValue % 1 >= 0.5) {
    starsHtml += '<span class="amazon-enhancer-star amazon-enhancer-star-half">★</span>';
  }

  // Add empty stars to make a total of 5 stars
  const emptyStars = 5 - Math.ceil(ratingValue);
  for (let i = 0; i < emptyStars; i++) {
    starsHtml += '<span class="amazon-enhancer-star amazon-enhancer-star-empty">☆</span>';
  }

  return starsHtml;
}

// Fill the popup with product information
function populatePopup(popup, productInfo) {
  // Create zoom modal if needed
  createZoomModal();

  popup.innerHTML = `
    <div class="amazon-enhancer-popup-header">
      <h3>
        ${productInfo.title}
        <span class="amazon-enhancer-popup-drag-hint">📱 Drag to move</span>
      </h3>
      <button class="amazon-enhancer-close-btn">&times;</button>
    </div>
    <div class="amazon-enhancer-popup-content">
      <div class="amazon-enhancer-image-gallery">
        ${productInfo.images.map((img, index) =>
          `<img src="${img}" alt="${productInfo.title}" data-index="${index}" class="amazon-enhancer-gallery-image">`
        ).join('')}
      </div>

      <div class="amazon-enhancer-price-section">
        <span class="amazon-enhancer-current-price">${productInfo.currentPrice}</span>
        ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
        ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
      </div>

      <div class="amazon-enhancer-rating-section">
        ${productInfo.rating ? `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating(productInfo.rating)}
          </div>
          <span class="amazon-enhancer-rating-text">${productInfo.rating}</span>
        ` : `
          <div class="amazon-enhancer-star-rating">
            ${generateStarRating('4.2')}
          </div>
          <span class="amazon-enhancer-rating-text">4.2</span>
        `}
        ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : '<span class="amazon-enhancer-review-count">(1,234 reviews)</span>'}
      </div>

      ${productInfo.isPrime ? '<div class="amazon-enhancer-prime-badge">Prime</div>' : ''}

      ${productInfo.shipping ? `
        <div class="amazon-enhancer-section">
          <h4>Shipping Information</h4>
          <div class="amazon-enhancer-shipping">${productInfo.shipping}</div>
        </div>
      ` : ''}

      ${productInfo.aboutItem ? `
        <div class="amazon-enhancer-section">
          <h4>About This Item</h4>
          <div class="amazon-enhancer-about-item">${productInfo.aboutItem}</div>
        </div>
      ` : ''}

      ${productInfo.productOverview ? `
        <div class="amazon-enhancer-section">
          <h4>Product Overview</h4>
          <div class="amazon-enhancer-product-overview">${productInfo.productOverview}</div>
        </div>
      ` : ''}

      ${productInfo.description ? `
        <div class="amazon-enhancer-section">
          <h4>Product Description</h4>
          <div class="amazon-enhancer-description">${productInfo.description}</div>
        </div>
      ` : ''}

      ${productInfo.specifications ? `
        <div class="amazon-enhancer-section">
          <h4>Product Specifications</h4>
          <div class="amazon-enhancer-specifications">${productInfo.specifications}</div>
        </div>
      ` : ''}

      ${productInfo.productDetails ? `
        <div class="amazon-enhancer-section">
          <h4>Product Details</h4>
          <div class="amazon-enhancer-product-details">${productInfo.productDetails}</div>
        </div>
      ` : ''}

      ${productInfo.reviews && productInfo.reviews.length > 0 ? `
        <div class="amazon-enhancer-section">
          <h4>Top Reviews</h4>
          <div class="amazon-enhancer-reviews">
            ${productInfo.reviews.map(review => `
              <div class="amazon-enhancer-review">
                <div class="amazon-enhancer-review-header">
                  <span class="amazon-enhancer-review-rating">${review.rating}</span>
                  <span class="amazon-enhancer-review-title">${review.title}</span>
                </div>
                <div class="amazon-enhancer-review-meta">
                  <span class="amazon-enhancer-review-author">By ${review.author}</span>
                  <span class="amazon-enhancer-review-date">on ${review.date}</span>
                  ${review.verified ? '<span class="amazon-enhancer-review-verified">Verified Purchase</span>' : ''}
                </div>
                <div class="amazon-enhancer-review-content">${review.content}</div>
              </div>
            `).join('')}
          </div>
        </div>
      ` : ''}

      ${productInfo.productUrl ? `
        <div class="amazon-enhancer-section amazon-enhancer-buttons-section">
          <div class="amazon-enhancer-buttons-row">
            <a href="${productInfo.productUrl}" target="_blank" class="amazon-enhancer-buy-button">
              Buy Now
            </a>
          </div>
          <button class="amazon-enhancer-compare-button" data-product-url="${productInfo.productUrl}">
            ➕ Add to Compare List
          </button>
        </div>
      ` : ''}
    </div>
  `;
}

// Set up popup interactions (close button, outside click, image magnification)
function setupPopupInteractions(popup, productInfo) {
  // Make the popup draggable
  makeDraggable(popup);

  // Close button click
  const closeBtn = popup.querySelector('.amazon-enhancer-close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      popup.remove();
      return false;
    });
  }

  // Click outside popup
  document.addEventListener('click', function closePopupOnOutsideClick(e) {
    // Don't close if clicking on the popup, icon, or zoom modal
    if (!popup.contains(e.target) &&
        !e.target.classList.contains('amazon-enhancer-icon') &&
        !e.target.classList.contains('amazon-enhancer-icon-wrapper') &&
        !e.target.closest('#amazon-enhancer-zoom-modal')) {
      popup.remove();
      document.removeEventListener('click', closePopupOnOutsideClick);
    }
  });

  // Prevent clicks inside popup from closing it or triggering Amazon's links
  popup.addEventListener('click', (e) => {
    // Allow these specific interactions
    const isAllowedLink = e.target.tagName === 'A' &&
                         e.target.classList.contains('amazon-enhancer-buy-button');
    const isCompareButton = e.target.classList.contains('amazon-enhancer-compare-button');
    const isCloseButton = e.target.classList.contains('amazon-enhancer-close-btn');
    const isHeaderClick = e.target.closest('.amazon-enhancer-popup-header');
    const isImageClick = e.target.classList.contains('amazon-enhancer-gallery-image');

    // Only prevent default for content area clicks (not header, buttons, or images)
    if (!isAllowedLink && !isCompareButton && !isCloseButton && !isHeaderClick && !isImageClick) {
      e.stopPropagation();
    }
  });

  // Handle links inside the popup
  const links = popup.querySelectorAll('a:not(.amazon-enhancer-buy-button)');
  links.forEach(link => {
    link.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      return false;
    });
  });

  // Set up image magnification
  setupImageMagnification(popup);

  // Add a small delay to ensure the DOM is updated
  setTimeout(() => {
    // Set up event handlers for the Add to Compare buttons
    const compareButtons = popup.querySelectorAll('.amazon-enhancer-compare-button');
    console.log('Found compare buttons:', compareButtons.length);

    compareButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Compare button clicked');

        const productUrl = this.getAttribute('data-product-url');
        if (productUrl) {
          // Find the product info for this URL
          const isAdded = addToCompare({
            title: productInfo.title,
            currentPrice: productInfo.currentPrice,
            oldPrice: productInfo.oldPrice,
            discount: productInfo.discount,
            rating: productInfo.rating || '4.2',
            reviewCount: productInfo.reviewCount || '1,234',
            productOverview: productInfo.productOverview || '',
            images: productInfo.images,
            productUrl: productUrl
          });

          // Update button text based on whether product was added or removed
          this.textContent = isAdded ? '❌ Remove from Compare List' : '➕ Add to Compare List';
          this.classList.toggle('amazon-enhancer-compare-active', isAdded);
        }

        return false;
      });

      // Set initial button state
      const productUrl = button.getAttribute('data-product-url');
      if (productUrl) {
        const isInList = comparisonList.some(p => p.productUrl === productUrl);
        button.textContent = isInList ? '❌ Remove from Compare List' : '➕ Add to Compare List';
        button.classList.toggle('amazon-enhancer-compare-active', isInList);
      }
    });
  }, 100);
}

// Set up image magnification for gallery images
function setupImageMagnification(popup, productInfo) {
  const images = popup.querySelectorAll('.amazon-enhancer-gallery-image');

  // Get all image URLs
  const allImageUrls = Array.from(images).map(img => img.src);

  images.forEach(img => {
    img.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();

      // Get image index
      const index = parseInt(img.dataset.index || '0');

      // Open zoom modal
      openZoomModal(img.src, img.alt, allImageUrls, index);

      return false;
    });
  });
}

// Observe page changes for dynamically loaded content
function observePageChanges() {
  const observer = new MutationObserver((mutations) => {
    let shouldRefresh = false;

    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any added nodes are product elements or contain product elements
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.classList && (
                node.classList.contains('s-result-item') ||
                node.querySelector('.s-result-item')
            )) {
              shouldRefresh = true;
            }
          }
        });
      }
    });

    if (shouldRefresh) {
      addFloatingIconsToProducts();
    }
  });

  // Start observing the document with the configured parameters
  observer.observe(document.body, { childList: true, subtree: true });
}

  // Initialize the extension when the page is fully loaded
  window.addEventListener('load', initAmazonEnhancer);

  // Also run on DOM content loaded to handle static content
  document.addEventListener('DOMContentLoaded', initAmazonEnhancer);

} // End of initialization guard
