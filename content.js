// Content script for Amazon Product Info Enhancer

// Main function to initialize the extension
function initAmazonEnhancer() {
  // Check if we're on an Amazon product listing page
  if (isProductListingPage()) {
    console.log('Amazon Product Info Enhancer: Product listing page detected');
    addFloatingIconsToProducts();

    // Add event listener for dynamic content loading (Amazon loads products dynamically)
    observePageChanges();
  }
}

// Check if the current page is a product listing page
function isProductListingPage() {
  // Skip product detail pages
  if (window.location.href.includes('/dp/') ||
      window.location.href.includes('/gp/product/')) {
    return false;
  }

  // Amazon search results page
  if (window.location.href.includes('/s?') ||
      window.location.href.includes('/s/') ||
      window.location.href.includes('/gp/bestsellers') ||
      window.location.href.includes('/gp/new-releases') ||
      window.location.href.includes('/deals') ||
      window.location.href.includes('/b?') ||
      window.location.href.includes('/stores/')) {
    return true;
  }

  // Check for product grid elements that typically appear on listing pages
  const productGrids = document.querySelectorAll(
    '.s-result-list, .s-search-results, .a-carousel, .octopus-pc-card-list, .a-list-normal'
  );

  // Check for product items
  const productItems = document.querySelectorAll(
    '.s-result-item, .a-carousel-card, .octopus-pc-item, .a-list-item'
  );

  return productGrids.length > 0 || productItems.length > 0;
}

// Add floating icons to all product images on the page
function addFloatingIconsToProducts() {
  // Find all product containers - using multiple selectors to cover different Amazon layouts
  const productElements = document.querySelectorAll(
    '.s-result-item, .a-carousel-card, .octopus-pc-item, .a-list-item'
  );

  productElements.forEach((product, index) => {
    // Skip if this is not a product container
    if (!product.querySelector('a')) return;

    // Find the product image container - using multiple selectors to cover different layouts
    const imageContainer = product.querySelector(
      '.s-image-container, .a-section.aok-relative, .a-link-normal .a-image-container, a.a-link-normal'
    );
    if (!imageContainer) return;

    // Check if we already added an icon to this product
    if (imageContainer.querySelector('.amazon-enhancer-icon')) return;

    // Make sure the image container has position relative for proper icon positioning
    if (window.getComputedStyle(imageContainer).position === 'static') {
      imageContainer.style.position = 'relative';
    }

    // Create floating icon
    const floatingIcon = document.createElement('div');
    floatingIcon.className = 'amazon-enhancer-icon';
    floatingIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>';

    // Add data attribute to identify the product
    floatingIcon.dataset.productIndex = index;

    // Add click event listener with stronger event prevention
    floatingIcon.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent triggering Amazon's click
      e.preventDefault(); // Prevent default link behavior

      // Stop event from bubbling up to parent elements
      if (e.stopImmediatePropagation) {
        e.stopImmediatePropagation();
      }

      // Show our popup
      showProductInfoPopup(product, e);

      // Return false to prevent default behavior and event bubbling
      return false;
    });

    // Also prevent mousedown events which Amazon might use for tracking
    floatingIcon.addEventListener('mousedown', (e) => {
      e.stopPropagation();
      e.preventDefault();
      if (e.stopImmediatePropagation) {
        e.stopImmediatePropagation();
      }
      return false;
    });

    // Append icon to image container
    imageContainer.appendChild(floatingIcon);
  });
}

// Show product info popup when icon is clicked
function showProductInfoPopup(productElement, clickEvent) {
  // Extract product information
  const productInfo = extractProductInfo(productElement);

  // Create popup element if it doesn't exist
  let popup = document.getElementById('amazon-enhancer-popup');
  if (!popup) {
    popup = document.createElement('div');
    popup.id = 'amazon-enhancer-popup';
    document.body.appendChild(popup);
  }

  // Position popup near the clicked icon
  positionPopup(popup, clickEvent);

  // Fill popup with product information
  populatePopup(popup, productInfo);

  // Add close button and outside click handler
  setupPopupInteractions(popup);
}

// Extract product information from the product element
function extractProductInfo(productElement) {
  // Initialize product info object
  const info = {
    title: '',
    images: [],
    currentPrice: '',
    oldPrice: '',
    discount: '',
    rating: '',
    reviewCount: '',
    isPrime: false,
    productDetails: '',
    aboutItem: '',
    description: '',
    shipping: ''
  };

  // Extract title
  const titleElement = productElement.querySelector('h2');
  if (titleElement) {
    info.title = titleElement.textContent.trim();
  }

  // Extract current price
  const priceElement = productElement.querySelector('.a-price .a-offscreen');
  if (priceElement) {
    info.currentPrice = priceElement.textContent.trim();
  }

  // Extract old price if available
  const oldPriceElement = productElement.querySelector('.a-price.a-text-price .a-offscreen');
  if (oldPriceElement) {
    info.oldPrice = oldPriceElement.textContent.trim();

    // Calculate discount percentage if both prices are available
    if (info.currentPrice && info.oldPrice) {
      try {
        const currentPriceValue = parseFloat(info.currentPrice.replace(/[^0-9.]/g, ''));
        const oldPriceValue = parseFloat(info.oldPrice.replace(/[^0-9.]/g, ''));
        if (!isNaN(currentPriceValue) && !isNaN(oldPriceValue) && oldPriceValue > 0) {
          const discountPercent = Math.round(((oldPriceValue - currentPriceValue) / oldPriceValue) * 100);
          info.discount = `${discountPercent}% off`;
        }
      } catch (e) {
        console.error('Error calculating discount:', e);
      }
    }
  }

  // Extract rating
  const ratingElement = productElement.querySelector('.a-icon-star-small');
  if (ratingElement) {
    info.rating = ratingElement.textContent.trim();
  }

  // Extract review count
  const reviewCountElement = productElement.querySelector('.a-size-base.s-underline-text');
  if (reviewCountElement) {
    info.reviewCount = reviewCountElement.textContent.trim();
  }

  // Check for Prime
  info.isPrime = !!productElement.querySelector('.s-prime');

  // We're not extracting seller information anymore

  // Extract main image and try to find additional images
  const imageElement = productElement.querySelector('.s-image');
  if (imageElement && imageElement.src) {
    info.images.push(imageElement.src);

    // Try to get higher resolution image
    const highResImage = imageElement.src.replace(/_AC_UL\d+_/, '_AC_UL1500_');
    if (highResImage !== imageElement.src) {
      info.images.push(highResImage);
    }

    // Get product URL to fetch more details
    const productLink = productElement.querySelector('a.a-link-normal');
    if (productLink && productLink.href) {
      // Store the product URL for fetching additional details
      info.productUrl = productLink.href;

      // Fetch additional product details
      fetchProductDetails(info.productUrl, info);
    }
  }

  return info;
}

// Fetch additional product details from the product page
function fetchProductDetails(productUrl, productInfo) {
  // Use the background script to fetch the product page
  chrome.runtime.sendMessage(
    {
      action: 'fetchProductPage',
      url: productUrl
    },
    response => {
      if (response && response.status === 'success' && response.html) {
        try {
          // Create a DOM parser to parse the HTML
          const parser = new DOMParser();
          const doc = parser.parseFromString(response.html, 'text/html');

          // Extract additional images
          const additionalImages = doc.querySelectorAll('#altImages img, #imageBlock img, #imgTagWrapperId img');
          additionalImages.forEach(img => {
            if (img.src && !productInfo.images.includes(img.src)) {
              // Get high-res version if possible
              const highResImg = img.src.replace(/_AC_US\d+_/, '_AC_US1500_')
                                        .replace(/_SX\d+_/, '_SX1500_')
                                        .replace(/_SY\d+_/, '_SY1500_');
              productInfo.images.push(highResImg);
            }
          });

          // Try to extract image URLs from the image gallery data
          try {
            const scripts = doc.querySelectorAll('script');
            scripts.forEach(script => {
              if (script.textContent.includes('ImageBlockATF') || script.textContent.includes('imageGalleryData')) {
                const matches = script.textContent.match(/"(https:\/\/m\.media-amazon\.com\/images\/I\/[^"]+)"/g);
                if (matches) {
                  matches.forEach(match => {
                    const imgUrl = match.replace(/"/g, '');
                    if (!productInfo.images.includes(imgUrl)) {
                      productInfo.images.push(imgUrl);
                    }
                  });
                }
              }
            });
          } catch (e) {
            console.error('Error extracting image gallery data:', e);
          }

          // Extract "About this item" section
          const aboutItemSection = doc.querySelector('#feature-bullets');
          if (aboutItemSection) {
            productInfo.aboutItem = aboutItemSection.textContent.trim();
          }

          // Extract product details section
          const productDetailsSection = doc.querySelector('#productDetails_techSpec_section_1, #detailBullets_feature_div, .a-section.a-spacing-small.a-spacing-top-small');
          if (productDetailsSection) {
            productInfo.productDetails = productDetailsSection.textContent.trim();
          }

          // Extract product description
          const descriptionSection = doc.querySelector('#productDescription');
          if (descriptionSection) {
            productInfo.description = descriptionSection.textContent.trim();
          }

          // Extract shipping information
          const deliveryBlock = doc.querySelector('#deliveryBlockMessage, #mir-layout-DELIVERY_BLOCK');
          if (deliveryBlock) {
            productInfo.shipping = deliveryBlock.textContent.trim();
          }

          // Update any popup that's currently showing this product
          updateProductInfoInPopup(productInfo);

        } catch (e) {
          console.error('Error parsing product page:', e);
          chrome.runtime.sendMessage({
            action: 'log',
            message: 'Error parsing product page: ' + e.toString()
          });
        }
      } else {
        console.error('Error fetching product page:', response);
        chrome.runtime.sendMessage({
          action: 'log',
          message: 'Error fetching product page: ' + JSON.stringify(response)
        });
      }
    }
  );
}

// Update product info in an open popup if it exists
function updateProductInfoInPopup(productInfo) {
  const popup = document.getElementById('amazon-enhancer-popup');
  if (popup) {
    // Check if this popup is for the same product
    const popupTitle = popup.querySelector('h3');
    if (popupTitle && popupTitle.textContent === productInfo.title) {
      // Update the popup with the new information
      populatePopup(popup, productInfo);
    }
  }
}

// Position the popup near the clicked icon
function positionPopup(popup, clickEvent) {
  const rect = clickEvent.target.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

  popup.style.top = (rect.bottom + scrollTop + 10) + 'px';
  popup.style.left = (rect.left + scrollLeft) + 'px';

  // Make sure popup is visible in viewport
  setTimeout(() => {
    const popupRect = popup.getBoundingClientRect();
    if (popupRect.right > window.innerWidth) {
      popup.style.left = (window.innerWidth - popupRect.width - 20) + 'px';
    }
    if (popupRect.bottom > window.innerHeight) {
      popup.style.top = (rect.top + scrollTop - popupRect.height - 10) + 'px';
    }
  }, 0);
}

// Fill the popup with product information
function populatePopup(popup, productInfo) {
  popup.innerHTML = `
    <div class="amazon-enhancer-popup-header">
      <h3>${productInfo.title}</h3>
      <button class="amazon-enhancer-close-btn">&times;</button>
    </div>
    <div class="amazon-enhancer-popup-content">
      <div class="amazon-enhancer-image-gallery">
        ${productInfo.images.map(img => `<img src="${img}" alt="${productInfo.title}">`).join('')}
      </div>

      <div class="amazon-enhancer-price-section">
        <span class="amazon-enhancer-current-price">${productInfo.currentPrice}</span>
        ${productInfo.oldPrice ? `<span class="amazon-enhancer-old-price">${productInfo.oldPrice}</span>` : ''}
        ${productInfo.discount ? `<span class="amazon-enhancer-discount">${productInfo.discount}</span>` : ''}
      </div>

      <div class="amazon-enhancer-rating-section">
        ${productInfo.rating ? `<span class="amazon-enhancer-rating">${productInfo.rating}</span>` : ''}
        ${productInfo.reviewCount ? `<span class="amazon-enhancer-review-count">(${productInfo.reviewCount})</span>` : ''}
      </div>

      ${productInfo.isPrime ? '<div class="amazon-enhancer-prime-badge">Prime</div>' : ''}

      ${productInfo.shipping ? `
        <div class="amazon-enhancer-section">
          <h4>Shipping Information</h4>
          <div class="amazon-enhancer-shipping">${productInfo.shipping}</div>
        </div>
      ` : ''}

      ${productInfo.aboutItem ? `
        <div class="amazon-enhancer-section">
          <h4>About This Item</h4>
          <div class="amazon-enhancer-about-item">${productInfo.aboutItem}</div>
        </div>
      ` : ''}

      ${productInfo.productDetails ? `
        <div class="amazon-enhancer-section">
          <h4>Product Details</h4>
          <div class="amazon-enhancer-product-details">${productInfo.productDetails}</div>
        </div>
      ` : ''}

      ${productInfo.description ? `
        <div class="amazon-enhancer-section">
          <h4>Product Description</h4>
          <div class="amazon-enhancer-description">${productInfo.description}</div>
        </div>
      ` : ''}

      ${productInfo.productUrl ? `
        <div class="amazon-enhancer-section">
          <a href="${productInfo.productUrl}" target="_blank" class="amazon-enhancer-view-button">
            View Full Product Page
          </a>
        </div>
      ` : ''}
    </div>
  `;
}

// Set up popup interactions (close button, outside click)
function setupPopupInteractions(popup) {
  // Close button click
  const closeBtn = popup.querySelector('.amazon-enhancer-close-btn');
  if (closeBtn) {
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      popup.remove();
      return false;
    });
  }

  // Click outside popup
  document.addEventListener('click', function closePopupOnOutsideClick(e) {
    if (!popup.contains(e.target) && !e.target.classList.contains('amazon-enhancer-icon')) {
      popup.remove();
      document.removeEventListener('click', closePopupOnOutsideClick);
    }
  });

  // Prevent clicks inside popup from closing it or triggering Amazon's links
  popup.addEventListener('click', (e) => {
    // Only stop propagation for clicks that aren't on links to the product page
    const isProductLink = e.target.tagName === 'A' &&
                         e.target.classList.contains('amazon-enhancer-view-button');

    if (!isProductLink) {
      e.stopPropagation();
      e.preventDefault();
    }
  });

  // Handle links inside the popup
  const links = popup.querySelectorAll('a:not(.amazon-enhancer-view-button)');
  links.forEach(link => {
    link.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      return false;
    });
  });
}

// Observe page changes for dynamically loaded content
function observePageChanges() {
  const observer = new MutationObserver((mutations) => {
    let shouldRefresh = false;

    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if any added nodes are product elements or contain product elements
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.classList && (
                node.classList.contains('s-result-item') ||
                node.querySelector('.s-result-item')
            )) {
              shouldRefresh = true;
            }
          }
        });
      }
    });

    if (shouldRefresh) {
      addFloatingIconsToProducts();
    }
  });

  // Start observing the document with the configured parameters
  observer.observe(document.body, { childList: true, subtree: true });
}

// Initialize the extension when the page is fully loaded
window.addEventListener('load', initAmazonEnhancer);

// Also run on DOM content loaded to handle static content
document.addEventListener('DOMContentLoaded', initAmazonEnhancer);
