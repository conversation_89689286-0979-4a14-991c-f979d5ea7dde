// Background script for Amazon Product Info Enhancer

// Listen for installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('Amazon Product Info Enhancer extension installed');
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getProductInfo') {
    // In a real implementation, you might fetch additional data from Amazon's API
    // or other sources here if needed
    sendResponse({ status: 'success' });
  } else if (request.action === 'fetchProductPage') {
    // Fetch product page content
    fetch(request.url)
      .then(response => response.text())
      .then(html => {
        sendResponse({ status: 'success', html: html });
      })
      .catch(error => {
        console.error('Error fetching product page:', error);
        sendResponse({ status: 'error', message: error.toString() });
      });
    return true; // Keep the message channel open for async responses
  } else if (request.action === 'log') {
    // Log messages from content script
    console.log('Content script log:', request.message);
    sendResponse({ status: 'success' });
  } else if (request.action === 'openTab') {
    // Open a new tab with the specified URL
    chrome.tabs.create({ url: request.url }, (tab) => {
      sendResponse({ status: 'success', tabId: tab.id });
    });
    return true; // Keep the message channel open for async response
  }
  return true; // Keep the message channel open for async responses
});

// Helper function to inject the content script into Amazon pages
function injectContentScript(tabId) {
  chrome.scripting.executeScript({
    target: { tabId: tabId },
    files: ['content.js']
  }).catch(err => console.error('Error injecting content script:', err));
}

// Listen for tab updates to inject content script when needed
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('amazon.com')) {
    // Inject content script if not already injected
    injectContentScript(tabId);
  }
});
