// Background script for Amazon Product Info Enhancer

// Listen for installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('Amazon Product Info Enhancer extension installed');
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getProductInfo') {
    // In a real implementation, you might fetch additional data from Amazon's API
    // or other sources here if needed
    sendResponse({ status: 'success' });
  } else if (request.action === 'fetchProductPage') {
    // Fetch product page content
    fetch(request.url)
      .then(response => response.text())
      .then(html => {
        sendResponse({ status: 'success', html: html });
      })
      .catch(error => {
        console.error('Error fetching product page:', error);
        sendResponse({ status: 'error', message: error.toString() });
      });
    return true; // Keep the message channel open for async responses
  } else if (request.action === 'log') {
    // Log messages from content script
    console.log('Content script log:', request.message);
    sendResponse({ status: 'success' });
  } else if (request.action === 'openTab') {
    // Open a new tab with the specified URL
    chrome.tabs.create({ url: request.url }, (tab) => {
      sendResponse({ status: 'success', tabId: tab.id });
    });
    return true; // Keep the message channel open for async response
  } else if (request.action === 'addToCart') {
    // Handle adding to cart by opening the product page and clicking the add to cart button
    addToCart(request.productUrl, sendResponse);
    return true; // Keep the message channel open for async response
  }
  return true; // Keep the message channel open for async responses
});

// Function to add a product to cart
function addToCart(productUrl, callback) {
  // Create a new tab with the product page
  chrome.tabs.create({ url: productUrl, active: false }, (tab) => {
    // Wait for the page to load
    chrome.tabs.onUpdated.addListener(function listener(tabId, changeInfo) {
      if (tabId === tab.id && changeInfo.status === 'complete') {
        // Remove the listener to avoid multiple executions
        chrome.tabs.onUpdated.removeListener(listener);

        // Execute a script to click the add to cart button
        chrome.scripting.executeScript({
          target: { tabId: tab.id },
          function: clickAddToCartButton
        }).then(results => {
          // Check if the button was found and clicked
          if (results && results[0] && results[0].result) {
            // Success - wait a moment for the cart to update
            setTimeout(() => {
              // Close the tab
              chrome.tabs.remove(tab.id);

              // Send success response
              if (callback) callback({ success: true });
            }, 2000);
          } else {
            // Failed to find or click the button
            chrome.tabs.remove(tab.id);
            if (callback) callback({
              success: false,
              error: 'Could not find or click the Add to Cart button'
            });
          }
        }).catch(error => {
          // Error executing the script
          chrome.tabs.remove(tab.id);
          if (callback) callback({
            success: false,
            error: 'Error executing script: ' + error.message
          });
        });
      }
    });
  });
}

// Function to be injected into the product page to click the Add to Cart button
function clickAddToCartButton() {
  // Try to find the Add to Cart button using various selectors
  const addToCartButton =
    document.querySelector('#add-to-cart-button') ||
    document.querySelector('input[name="submit.add-to-cart"]') ||
    document.querySelector('span[data-action="add-to-cart"]') ||
    document.querySelector('input[value="Add to Cart"]') ||
    document.querySelector('input[aria-labelledby="submit.add-to-cart-announce"]') ||
    document.querySelector('span[id="submit.add-to-cart"]') ||
    document.querySelector('span[id="submit.add-to-cart-announce"]');

  if (addToCartButton) {
    // Click the button
    addToCartButton.click();
    return true;
  }

  return false;
}

// Helper function to inject the content script into Amazon pages
function injectContentScript(tabId) {
  chrome.scripting.executeScript({
    target: { tabId: tabId },
    files: ['content.js']
  }).catch(err => console.error('Error injecting content script:', err));
}

// Listen for tab updates to inject content script when needed
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('amazon.com')) {
    // Inject content script if not already injected
    injectContentScript(tabId);
  }
});
