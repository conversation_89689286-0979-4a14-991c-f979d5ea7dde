# Amazon Product Info Enhancer

A Chrome extension that enhances the Amazon browsing experience by showing key product information directly on product listing pages.

## Features

- **Floating Icon on Product Images**: Automatically injects a small, modern floating icon on the corner of every product image in Amazon search results and category pages.

- **Comprehensive Product Info Popup**: When clicking the icon, displays a neat popup with detailed product information:
  - Product title
  - All available product images (with a scrollable gallery)
  - Current price, old price (if available), and calculated discount percentage
  - Average rating (stars) and number of ratings
  - Prime eligibility indicator
  - "About this item" section
  - Product details
  - Product description
  - Shipping information

## Installation

1. Add your own icon files to the `icons` folder:
   - `icon16.png` (16x16 pixels)
   - `icon48.png` (48x48 pixels)
   - `icon128.png` (128x128 pixels)

2. Load the extension in Chrome:
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" (toggle in the top-right corner)
   - Click "Load unpacked" and select the extension folder

## Usage

1. Browse Amazon:
   - Go to any Amazon product listing page (search results, category pages, etc.)
   - Hover over product images to see the info icon
   - Click the icon to view detailed product information in a popup

2. The popup will display all available information about the product, and you can:
   - View all product images in a scrollable gallery
   - See pricing information including discounts
   - Check ratings and reviews
   - View shipping information
   - Read about the product features and description
   - Click to view the full product page if needed

## Files Structure

- `manifest.json`: Extension configuration
- `background.js`: Background script for handling requests and communication
- `content.js`: Content script that injects the floating icons and popups
- `popup.html` & `popup.js`: Extension popup UI
- `styles.css`: Styling for injected elements
- `icons/`: Folder for extension icons (add your own icons here)

## Notes for Adding Icons

- Create square PNG images for the following sizes:
  - 16x16 pixels (icon16.png)
  - 48x48 pixels (icon48.png)
  - 128x128 pixels (icon128.png)
- Place these files in the `icons` folder
- The icons will be used for the extension in the Chrome toolbar and extensions page
