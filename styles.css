/* Styles for Amazon Product Info Enhancer */

/* Floating icon on product images */
.amazon-enhancer-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 153, 0, 0.9); /* Amazon orange with transparency */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 9999; /* Higher z-index to ensure it's above Amazon elements */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: transform 0.2s ease, background-color 0.2s ease;
  pointer-events: auto; /* Ensure clicks are captured */
  border: 2px solid white; /* White border to make it stand out */
}

.amazon-enhancer-icon:hover {
  transform: scale(1.15);
  background-color: rgba(255, 153, 0, 1);
}

.amazon-enhancer-icon svg {
  width: 22px;
  height: 22px;
  color: white;
  pointer-events: none; /* Prevent SVG from capturing clicks */
}

/* Product info popup */
#amazon-enhancer-popup {
  position: absolute;
  width: 400px;
  max-width: 90vw;
  max-height: 80vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.35);
  z-index: 2147483647; /* Maximum z-index value to ensure it's above everything */
  overflow: hidden;
  animation: amazon-enhancer-fade-in 0.2s ease;
  font-family: Arial, sans-serif;
}

@keyframes amazon-enhancer-fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.amazon-enhancer-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #232f3e; /* Amazon dark blue */
  color: white;
}

.amazon-enhancer-popup-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 280px;
}

.amazon-enhancer-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  margin: 0;
  line-height: 1;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amazon-enhancer-popup-content {
  padding: 15px;
  max-height: calc(80vh - 50px); /* 50px is approximately the header height */
  overflow-y: auto;
  overscroll-behavior: contain; /* Prevents scrolling the page when at the end of the popup content */
}

.amazon-enhancer-image-gallery {
  display: flex;
  overflow-x: auto;
  margin-bottom: 15px;
  padding-bottom: 5px;
  scrollbar-width: thin;
}

.amazon-enhancer-image-gallery img {
  height: 120px;
  margin-right: 10px;
  border-radius: 4px;
  object-fit: contain;
}

.amazon-enhancer-price-section {
  margin-bottom: 10px;
}

.amazon-enhancer-current-price {
  font-size: 18px;
  font-weight: bold;
  color: #B12704; /* Amazon price red */
}

.amazon-enhancer-old-price {
  font-size: 14px;
  text-decoration: line-through;
  color: #565959;
  margin-left: 8px;
}

.amazon-enhancer-rating-section {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.amazon-enhancer-rating {
  color: #FFA41C; /* Amazon star color */
  margin-right: 5px;
}

.amazon-enhancer-review-count {
  color: #007185; /* Amazon link blue */
  font-size: 14px;
}

.amazon-enhancer-prime-badge {
  display: inline-block;
  background-color: #00A8E1; /* Prime blue */
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  margin-bottom: 10px;
}

.amazon-enhancer-section {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #e7e7e7;
}

.amazon-enhancer-section h4 {
  font-size: 14px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #0F1111;
}

.amazon-enhancer-seller,
.amazon-enhancer-shipping,
.amazon-enhancer-about-item,
.amazon-enhancer-description {
  font-size: 14px;
  color: #565959;
  margin-bottom: 8px;
  line-height: 1.4;
}

.amazon-enhancer-about-item,
.amazon-enhancer-description {
  max-height: 150px;
  overflow-y: auto;
  padding-right: 5px;
}

.amazon-enhancer-discount {
  color: #B12704;
  font-weight: bold;
  margin-left: 8px;
  font-size: 14px;
}

.amazon-enhancer-view-button {
  display: block;
  background-color: #FFD814;
  border: 1px solid #FCD200;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(213, 217, 217, .5);
  color: #0F1111;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  padding: 8px 10px;
  margin: 10px 0;
  transition: all 0.2s ease;
}

.amazon-enhancer-view-button:hover {
  background-color: #F7CA00;
  border-color: #F2C200;
}

/* Scrollbar styling */
.amazon-enhancer-popup-content::-webkit-scrollbar,
.amazon-enhancer-image-gallery::-webkit-scrollbar,
.amazon-enhancer-about-item::-webkit-scrollbar,
.amazon-enhancer-description::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.amazon-enhancer-popup-content::-webkit-scrollbar-track,
.amazon-enhancer-image-gallery::-webkit-scrollbar-track,
.amazon-enhancer-about-item::-webkit-scrollbar-track,
.amazon-enhancer-description::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.amazon-enhancer-popup-content::-webkit-scrollbar-thumb,
.amazon-enhancer-image-gallery::-webkit-scrollbar-thumb,
.amazon-enhancer-about-item::-webkit-scrollbar-thumb,
.amazon-enhancer-description::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.amazon-enhancer-popup-content::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-image-gallery::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-about-item::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-description::-webkit-scrollbar-thumb:hover {
  background: #555;
}
