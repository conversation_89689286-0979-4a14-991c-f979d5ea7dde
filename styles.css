/* Styles for Amazon Product Info Enhancer */

/* Floating icon on product images */
.amazon-enhancer-icon-wrapper {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 9999;
  pointer-events: auto;
}

.amazon-enhancer-icon {
  width: 32px;
  height: 32px;
  background-color: rgba(255, 153, 0, 0.9); /* Amazon orange with transparency */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: opacity 0.2s ease, transform 0.2s ease, background-color 0.2s ease;
  opacity: 0; /* Hidden by default, shown on hover */
  border: 2px solid white; /* White border to make it stand out */
}

/* Show icon on hover over the parent container or wrapper */
.s-image-container:hover .amazon-enhancer-icon,
.a-section.aok-relative:hover .amazon-enhancer-icon,
.amazon-enhancer-icon-wrapper:hover .amazon-enhancer-icon,
.amazon-enhancer-icon:hover {
  opacity: 1;
}

.amazon-enhancer-icon:hover {
  transform: scale(1.1);
  background-color: rgba(255, 153, 0, 1);
}

.amazon-enhancer-icon svg {
  width: 20px;
  height: 20px;
  color: white;
}

/* Product info popup */
#amazon-enhancer-popup {
  position: absolute;
  width: 400px;
  max-width: 90vw;
  max-height: 80vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.35);
  z-index: 2147483647; /* Maximum z-index value to ensure it's above everything */
  overflow: hidden;
  animation: amazon-enhancer-fade-in 0.2s ease;
  font-family: Arial, sans-serif;
}

@keyframes amazon-enhancer-fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.amazon-enhancer-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #232f3e; /* Amazon dark blue */
  color: white;
}

.amazon-enhancer-popup-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 280px;
}

.amazon-enhancer-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  margin: 0;
  line-height: 1;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amazon-enhancer-popup-content {
  padding: 15px;
  max-height: calc(80vh - 50px); /* 50px is approximately the header height */
  overflow-y: auto;
  overscroll-behavior: contain; /* Prevents scrolling the page when at the end of the popup content */
}

.amazon-enhancer-image-gallery {
  display: flex;
  overflow-x: auto;
  margin-bottom: 15px;
  padding-bottom: 5px;
  scrollbar-width: thin;
  position: relative; /* For positioning the magnified image */
}

.amazon-enhancer-image-gallery img {
  height: 120px;
  margin-right: 10px;
  border-radius: 4px;
  object-fit: contain;
  cursor: zoom-in;
  transition: transform 0.2s ease;
}

/* Image magnification */
.amazon-enhancer-image-gallery img:hover {
  transform: scale(1.05);
}

/* Image zoom modal */
#amazon-enhancer-zoom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 2147483647;
  display: none;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

#amazon-enhancer-zoom-modal.active {
  display: flex;
}

#amazon-enhancer-zoom-image {
  max-width: 90%;
  max-height: 80%;
  object-fit: contain;
}

#amazon-enhancer-zoom-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

#amazon-enhancer-zoom-close:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

#amazon-enhancer-zoom-controls {
  display: flex;
  margin-top: 20px;
}

#amazon-enhancer-zoom-prev,
#amazon-enhancer-zoom-next {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 20px;
  margin: 0 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

#amazon-enhancer-zoom-prev:hover,
#amazon-enhancer-zoom-next:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

.amazon-enhancer-price-section {
  margin-bottom: 10px;
}

.amazon-enhancer-current-price {
  font-size: 18px;
  font-weight: bold;
  color: #B12704; /* Amazon price red */
}

.amazon-enhancer-old-price {
  font-size: 14px;
  text-decoration: line-through;
  color: #565959;
  margin-left: 8px;
}

.amazon-enhancer-rating-section {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.amazon-enhancer-star-rating {
  display: flex;
  margin-right: 5px;
  font-size: 18px;
  line-height: 1;
}

.amazon-enhancer-star {
  margin-right: 2px;
}

.amazon-enhancer-star-full {
  color: #FFA41C; /* Amazon star color */
}

.amazon-enhancer-star-half {
  color: #FFA41C;
  position: relative;
}

.amazon-enhancer-star-empty {
  color: #E7E7E7;
}

.amazon-enhancer-rating-text {
  color: #FFA41C; /* Amazon star color */
  margin-right: 5px;
  font-weight: bold;
}

.amazon-enhancer-review-count {
  color: #007185; /* Amazon link blue */
  font-size: 14px;
}

.amazon-enhancer-prime-badge {
  display: inline-block;
  background-color: #00A8E1; /* Prime blue */
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  margin-bottom: 10px;
}

.amazon-enhancer-section {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #e7e7e7;
}

.amazon-enhancer-section h4 {
  font-size: 14px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #0F1111;
}

.amazon-enhancer-shipping,
.amazon-enhancer-about-item,
.amazon-enhancer-description,
.amazon-enhancer-specifications,
.amazon-enhancer-product-details,
.amazon-enhancer-product-overview,
.amazon-enhancer-review-content {
  font-size: 14px;
  color: #565959;
  margin-bottom: 8px;
  line-height: 1.4;
}

.amazon-enhancer-about-item,
.amazon-enhancer-description,
.amazon-enhancer-specifications,
.amazon-enhancer-product-details,
.amazon-enhancer-product-overview,
.amazon-enhancer-reviews {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 5px;
}

/* Make sure expanded content is visible */
.amazon-enhancer-product-overview .a-expander-content,
.amazon-enhancer-review-content .a-expander-content {
  display: block !important;
  max-height: none !important;
}

/* Reviews styling */
.amazon-enhancer-reviews {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.amazon-enhancer-review {
  padding: 10px;
  border-radius: 4px;
  background-color: #f7f7f7;
  border: 1px solid #e7e7e7;
}

.amazon-enhancer-review-header {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.amazon-enhancer-review-rating {
  color: #FFA41C;
  margin-right: 8px;
  font-weight: bold;
}

.amazon-enhancer-review-title {
  font-weight: bold;
  color: #0F1111;
}

.amazon-enhancer-review-meta {
  font-size: 12px;
  color: #565959;
  margin-bottom: 8px;
}

.amazon-enhancer-review-author {
  margin-right: 5px;
}

.amazon-enhancer-review-date {
  margin-right: 5px;
}

.amazon-enhancer-review-verified {
  color: #C45500;
  font-weight: bold;
}

.amazon-enhancer-review-content {
  font-size: 14px;
  line-height: 1.4;
}

.amazon-enhancer-discount {
  color: #B12704;
  font-weight: bold;
  margin-left: 8px;
  font-size: 14px;
}

.amazon-enhancer-buttons-section {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;
  margin-top: 15px;
  margin-bottom: 15px;
}

/* Make sure buttons are properly sized in the buttons section */
.amazon-enhancer-buttons-row {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
}

.amazon-enhancer-buttons-row .amazon-enhancer-buy-button {
  flex: 1;
  min-width: 100px;
}

.amazon-enhancer-buttons-section .amazon-enhancer-compare-button {
  width: 100%;
  margin-top: 10px;
}



.amazon-enhancer-buy-button {
  flex: 1;
  display: block !important;
  background-color: #FF9900 !important;
  border: 1px solid #FF8000 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 5px rgba(213, 217, 217, .5) !important;
  color: white !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  text-align: center !important;
  text-decoration: none !important;
  padding: 8px 10px !important;
  margin: 10px 0 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  z-index: 100 !important;
  width: auto !important;
  min-width: 120px !important;
  max-width: none !important;
  height: auto !important;
  line-height: normal !important;
  float: none !important;
  position: static !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.amazon-enhancer-buy-button:hover {
  background-color: #FF8000 !important;
  border-color: #FF6600 !important;
  text-decoration: none !important;
  color: white !important;
}

.amazon-enhancer-buy-button:disabled {
  opacity: 0.7 !important;
  cursor: default !important;
}

/* Loading indicators */
.amazon-enhancer-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.amazon-enhancer-loading-details {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
  margin-top: 15px;
  border-top: 1px solid #e7e7e7;
}

.amazon-enhancer-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 153, 0, 0.2);
  border-radius: 50%;
  border-top-color: #ff9900;
  animation: amazon-enhancer-spin 1s linear infinite;
  margin-bottom: 15px;
}

.amazon-enhancer-spinner-small {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 153, 0, 0.2);
  border-radius: 50%;
  border-top-color: #ff9900;
  animation: amazon-enhancer-spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes amazon-enhancer-spin {
  to { transform: rotate(360deg); }
}

/* Compare button styles */
.amazon-enhancer-compare-button {
  flex: 1;
  display: block !important;
  background-color: #4CAF50 !important; /* Green color for better visibility */
  border: 1px solid #388E3C !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 5px rgba(213, 217, 217, .5) !important;
  color: white !important;
  font-size: 16px !important; /* Larger font */
  font-weight: 600 !important; /* Bolder text */
  text-align: center !important;
  text-decoration: none !important;
  padding: 12px 15px !important; /* Larger padding */
  margin: 10px 0 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  z-index: 100 !important;
  width: 100% !important;
  min-width: 120px !important;
  max-width: none !important;
  height: auto !important;
  line-height: normal !important;
  float: none !important;
  position: static !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.amazon-enhancer-compare-button:hover {
  background-color: #388E3C !important;
  border-color: #2E7D32 !important;
  text-decoration: none !important;
  color: white !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

.amazon-enhancer-compare-active {
  background-color: #F44336 !important; /* Red for remove action */
  border-color: #D32F2F !important;
}

/* Floating compare button */
#amazon-enhancer-compare-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #4CAF50; /* Green to match the add button */
  color: white;
  border-radius: 50px;
  padding: 15px 25px; /* Larger padding */
  display: flex;
  align-items: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  z-index: 2147483646;
  transition: all 0.3s ease;
  font-family: Arial, sans-serif;
  font-size: 16px; /* Larger font */
  font-weight: 600; /* Bolder text */
  border: none;
  outline: none;
  animation: amazon-enhancer-pulse 2s infinite; /* Add pulsing animation */
}

@keyframes amazon-enhancer-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

#amazon-enhancer-compare-button:hover {
  background-color: #388E3C;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.amazon-enhancer-compare-count {
  background-color: #FF5722; /* Orange for contrast */
  color: white;
  border-radius: 50%;
  width: 28px; /* Larger size */
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 10px;
  font-size: 16px;
}

.amazon-enhancer-compare-text {
  font-weight: 500;
}

/* Comparison popup */
#amazon-enhancer-comparison-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 90%;
  max-width: 1000px; /* Reduced to force scrolling */
  max-height: 90vh;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  z-index: 2147483647;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  border: 2px solid #4CAF50; /* Green border to match the buttons */
}

#amazon-enhancer-comparison-popup.active {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) scale(1);
  animation: amazon-enhancer-popup-appear 0.3s ease-out;
}

@keyframes amazon-enhancer-popup-appear {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.amazon-enhancer-comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #4CAF50; /* Green to match the buttons */
  color: white;
  border-bottom: 2px solid #388E3C;
}

.amazon-enhancer-comparison-header h3 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.amazon-enhancer-comparison-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  margin: 0;
  line-height: 1;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.amazon-enhancer-comparison-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.amazon-enhancer-comparison-content {
  padding: 20px;
  max-height: calc(90vh - 70px);
  overflow-x: scroll; /* Force horizontal scrollbar */
  overflow-y: auto;
  scrollbar-width: auto; /* Firefox */
  -ms-overflow-style: auto; /* IE/Edge */
  scrollbar-gutter: stable; /* Force scrollbar space */
}

.amazon-enhancer-comparison-table-wrapper {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
}

.amazon-enhancer-comparison-table {
  width: max-content; /* Allow table to expand beyond container */
  min-width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  table-layout: auto; /* Auto layout for dynamic content */
}

.amazon-enhancer-comparison-table th,
.amazon-enhancer-comparison-table td {
  padding: 15px;
  border: 1px solid #e7e7e7;
  text-align: left;
  vertical-align: top;
}

.amazon-enhancer-comparison-table th {
  background-color: #f0f8ff; /* Light blue background */
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #4CAF50; /* Green border to match theme */
}

.amazon-enhancer-comparison-table th:first-child,
.amazon-enhancer-comparison-table td:first-child {
  width: 180px;
  min-width: 180px;
  max-width: 180px;
  background-color: #f0f8ff;
  font-weight: 600;
  color: #333;
  position: sticky;
  left: 0;
  z-index: 10;
  white-space: nowrap;
}

/* Product columns should have equal width */
.amazon-enhancer-comparison-table th:not(:first-child),
.amazon-enhancer-comparison-table td:not(:first-child) {
  width: 350px;
  min-width: 350px;
  max-width: 350px;
}

/* Zebra striping for better readability */
.amazon-enhancer-comparison-table tr:nth-child(even) td {
  background-color: #f9f9f9;
}

/* Ensure first column maintains its background even with zebra striping */
.amazon-enhancer-comparison-table tr:nth-child(even) td:first-child {
  background-color: #f0f8ff;
}

.amazon-enhancer-comparison-product-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding-right: 20px;
}

.amazon-enhancer-comparison-product-header img {
  width: 100px;
  height: 100px;
  object-fit: contain;
  margin-bottom: 10px;
}

.amazon-enhancer-comparison-product-title {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  max-height: 60px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

.amazon-enhancer-comparison-remove {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: none;
  color: #565959;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin: 0;
  line-height: 1;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amazon-enhancer-comparison-remove:hover {
  color: #B12704;
}

.amazon-enhancer-comparison-price {
  display: flex;
  flex-direction: column;
}

.amazon-enhancer-comparison-current-price {
  font-size: 16px;
  font-weight: bold;
  color: #B12704;
}

.amazon-enhancer-comparison-old-price {
  font-size: 14px;
  text-decoration: line-through;
  color: #565959;
}

.amazon-enhancer-comparison-discount {
  color: #B12704;
  font-weight: bold;
  font-size: 14px;
}

.amazon-enhancer-comparison-overview {
  font-size: 14px;
  color: #565959;
  max-height: 120px;
  overflow: hidden;
  line-height: 1.4;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  line-clamp: 6;
  -webkit-box-orient: vertical;
}

.amazon-enhancer-comparison-actions {
  display: flex;
  justify-content: center;
}

.amazon-enhancer-comparison-view {
  display: inline-block;
  background-color: #FFD814;
  border: 1px solid #FCD200;
  border-radius: 8px;
  color: #0F1111;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  padding: 8px 15px;
  transition: all 0.2s ease;
  flex: 1;
}

.amazon-enhancer-comparison-view:hover {
  background-color: #F7CA00;
  border-color: #F2C200;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.amazon-enhancer-comparison-buy {
  display: inline-block;
  background-color: #FF9900;
  border: 1px solid #FF8000;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  padding: 10px 20px;
  transition: all 0.2s ease;
  min-width: 120px;
}

.amazon-enhancer-comparison-buy:hover {
  background-color: #FF8000;
  border-color: #FF6600;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Scrollbar styling */
.amazon-enhancer-popup-content::-webkit-scrollbar,
.amazon-enhancer-image-gallery::-webkit-scrollbar,
.amazon-enhancer-about-item::-webkit-scrollbar,
.amazon-enhancer-description::-webkit-scrollbar,
.amazon-enhancer-specifications::-webkit-scrollbar,
.amazon-enhancer-product-details::-webkit-scrollbar,
.amazon-enhancer-product-overview::-webkit-scrollbar,
.amazon-enhancer-reviews::-webkit-scrollbar,
.amazon-enhancer-comparison-content::-webkit-scrollbar,
.amazon-enhancer-comparison-table-wrapper::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}



.amazon-enhancer-popup-content::-webkit-scrollbar-track,
.amazon-enhancer-image-gallery::-webkit-scrollbar-track,
.amazon-enhancer-about-item::-webkit-scrollbar-track,
.amazon-enhancer-description::-webkit-scrollbar-track,
.amazon-enhancer-specifications::-webkit-scrollbar-track,
.amazon-enhancer-product-details::-webkit-scrollbar-track,
.amazon-enhancer-product-overview::-webkit-scrollbar-track,
.amazon-enhancer-reviews::-webkit-scrollbar-track,
.amazon-enhancer-comparison-content::-webkit-scrollbar-track,
.amazon-enhancer-comparison-table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.amazon-enhancer-popup-content::-webkit-scrollbar-thumb,
.amazon-enhancer-image-gallery::-webkit-scrollbar-thumb,
.amazon-enhancer-about-item::-webkit-scrollbar-thumb,
.amazon-enhancer-description::-webkit-scrollbar-thumb,
.amazon-enhancer-specifications::-webkit-scrollbar-thumb,
.amazon-enhancer-product-details::-webkit-scrollbar-thumb,
.amazon-enhancer-product-overview::-webkit-scrollbar-thumb,
.amazon-enhancer-reviews::-webkit-scrollbar-thumb,
.amazon-enhancer-comparison-content::-webkit-scrollbar-thumb,
.amazon-enhancer-comparison-table-wrapper::-webkit-scrollbar-thumb {
  background: #4CAF50;
  border-radius: 4px;
}

.amazon-enhancer-popup-content::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-image-gallery::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-about-item::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-description::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-specifications::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-product-details::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-product-overview::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-reviews::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-comparison-content::-webkit-scrollbar-thumb:hover,
.amazon-enhancer-comparison-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #388E3C;
}
